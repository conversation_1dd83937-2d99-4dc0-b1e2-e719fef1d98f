# Use layernext-python-sdk to test quickbooks_get_auth_uri method
from layernext import LayerNextClient
from utils.metalake import quickbooks_create_vendor, quickbooks_create_bill

# Create vendor object

""" vendor_dict = {
    "PrimaryEmailAddr": {"Address": "<EMAIL>"},
    "WebAddr": {"URI": "http://DiannesAutoShop.com"},
    "PrimaryPhone": {"FreeFormNumber": "(*************"},
    "DisplayName": "<PERSON><PERSON>'s Auto Shop",
    "Suffix": "Sr.",
    "Title": "Ms.",
    "Mobile": {"FreeFormNumber": "(*************"},
    "FamilyName": "Bradley",
    "TaxIdentifier": "99-5688293",
    "AcctNum": "35372649",
    "CompanyName": "<PERSON><PERSON>'s Auto Shop",
    "BillAddr": {
        "City": "Millbrae",
        "Country": "U.S.A",
        "Line3": "29834 Mustang Ave.",
        "Line2": "<PERSON><PERSON>",
        "Line1": "<PERSON><PERSON>'s Auto Shop",
        "PostalCode": "94030",
        "CountrySubDivisionCode": "CA",
    },
    "GivenName": "Dianne",
    "PrintOnCheckName": "Dianne's Auto Shop",
} 
vendor_dict = {
    "PrimaryEmailAddr": {"Address": "<EMAIL>"},
    "WebAddr": {"URI": "http://SmithAutoCare.com"},
    "PrimaryPhone": {"FreeFormNumber": "(*************"},
    "DisplayName": "Smith Auto Care",
    "Suffix": "Jr.",
    "Title": "Mr.",
    "Mobile": {"FreeFormNumber": "(*************"},
    "FamilyName": "Smith",
    "TaxIdentifier": "88-1234567",
    "AcctNum": "72839401",
    "CompanyName": "Smith Auto Care",
    "BillAddr": {
        "City": "Toronto",
        "Country": "Canada",
        "Line3": "101 Queen Street West",
        "Line2": "John Smith",
        "Line1": "Smith Auto Care",
        "PostalCode": "M5H 2N2",
        "CountrySubDivisionCode": "ON",
    },
    "GivenName": "John",
    "PrintOnCheckName": "Smith Auto Care",
}


vendor_create_res = quickbooks_create_vendor(vendor_dict)
print(vendor_create_res)"""

# Create bill for vendor 59
bill_dict = {
    "Line": [
        {
            "DetailType": "AccountBasedExpenseLineDetail",
            "Amount": 244.87,
            "Id": "1",
            "AccountBasedExpenseLineDetail": {"AccountRef": {"value": "7"}},
        }
    ],
    "VendorRef": {"value": "59"},
}
bill_create_res = quickbooks_create_bill(bill_dict)
print(bill_create_res)
