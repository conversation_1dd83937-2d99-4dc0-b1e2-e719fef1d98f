"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* websocket_service.py handles WebSocket connections and message broadcasting

* @class WebSocketService
* @description This class manages WebSocket connections and provides methods to send messages to connected clients
* <AUTHOR> Assistant
"""

import asyncio
from datetime import datetime
import json
import uuid
from typing import Dict, Set, Optional, Any, Union, Tuple
from fastapi import WebSocket
from utils.constant import WebSocketMessageType
from utils.logger import get_debug_logger
from services.singleton_meta import SingletonMeta
from services.socketio_auth_service import SocketIOAuthService
from enum import Enum

logger = get_debug_logger("websocket_service", "./logs/websocket.log")


class WebSocketService(metaclass=SingletonMeta):
    """
    Service class to manage WebSocket connections and handle message broadcasting
    """

    def __init__(self):
        # Dictionary to store active WebSocket connections
        # Key: connection_id (UUID), Value: WebSocket instance
        self.active_connections: Dict[str, WebSocket] = {}

        # Set to track connection IDs for quick lookup
        self.connection_ids: Set[str] = set()

        # Socket.IO server instance (will be set from app.py)
        self.socketio_server = None

        # Socket.IO authentication service
        self.socketio_auth_service = SocketIOAuthService()

        # Dictionary to store authenticated Socket.IO sessions
        # Key: sid, Value: user_info
        self.authenticated_socketio_sessions: Dict[str, Dict[str, Any]] = {}

        # Dictionary to store conversation-specific Socket.IO sessions
        # Key: conversation_id, Value: Set of sids
        self.conversation_specific_socketio_sessions: Dict[str, set[str]] = {}

        # Initialize orchestration agent as class variable
        from services.orchestration_agent.orchestration_agent import OrchestrationAgent

        self.orchestration_agent = OrchestrationAgent()

        logger.info("WebSocketService initialized")

    def set_socketio_server(self, socketio_server):
        """
        Set the Socket.IO server instance for broadcasting

        Args:
            socketio_server: The Socket.IO AsyncServer instance
        """
        self.socketio_server = socketio_server
        logger.info("Socket.IO server instance set in WebSocketService")

    async def connect(self, websocket: WebSocket) -> str:
        """
        Register a new WebSocket connection

        Args:
            websocket: The WebSocket connection instance

        Returns:
            str: Unique connection ID
        """
        connection_id = str(uuid.uuid4())

        self.active_connections[connection_id] = websocket
        self.connection_ids.add(connection_id)

        logger.info(f"WebSocket connection registered: {connection_id}")
        logger.info(f"Total active connections: {len(self.active_connections)}")

        # Send welcome message to the newly connected client
        welcome_message = {
            "type": "connection_established",
            "connection_id": connection_id,
            "message": "Successfully connected to WebSocket server",
            "timestamp": asyncio.get_event_loop().time(),
        }

        await self.send_to_connection(connection_id, welcome_message)

        return connection_id

    async def disconnect(self, connection_id: str):
        """
        Remove a WebSocket connection

        Args:
            connection_id: The connection ID to remove
        """
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
            self.connection_ids.discard(connection_id)

            logger.info(f"WebSocket connection removed: {connection_id}")
            logger.info(f"Total active connections: {len(self.active_connections)}")
        else:
            logger.warning(f"Attempted to remove non-existent connection: {connection_id}")

    async def send_to_connection(self, connection_id: str, message: Any):
        """
        Send a message to a specific WebSocket connection

        Args:
            connection_id: The target connection ID
            message: The message to send (dict will be JSON serialized)
        """
        if connection_id not in self.active_connections:
            logger.warning(f"Cannot send message to non-existent connection: {connection_id}")
            return False

        websocket = self.active_connections[connection_id]

        try:
            # Convert message to JSON string if it's a dict
            if isinstance(message, dict):
                message_text = json.dumps(message)
            else:
                message_text = str(message)

            await websocket.send_text(message_text)
            logger.debug(f"Message sent to connection {connection_id}: {message_text}")
            return True

        except Exception as e:
            logger.error(f"Failed to send message to connection {connection_id}: {str(e)}")
            # Remove the connection if it's no longer valid
            await self.disconnect(connection_id)
            return False

    async def broadcast_to_all(self, message: Any):
        """
        Send a message to all connected WebSocket clients and Socket.IO clients

        Args:
            message: The message to broadcast (dict will be JSON serialized)
        """
        total_connections = len(self.active_connections)
        socketio_available = self.socketio_server is not None

        if total_connections == 0 and not socketio_available:
            logger.info("No active connections to broadcast to")
            return

        logger.info(f"Broadcasting message to {total_connections} WebSocket connections and Socket.IO clients")

        # Broadcast to native WebSocket connections
        websocket_tasks = []
        if total_connections > 0:
            for connection_id in list(self.active_connections.keys()):
                task = self.send_to_connection(connection_id, message)
                websocket_tasks.append(task)

        # Broadcast to Socket.IO connections
        socketio_task = None
        if socketio_available:
            socketio_task = self._broadcast_to_socketio(message)

        # Execute all send operations concurrently
        all_tasks = websocket_tasks + ([socketio_task] if socketio_task else [])
        if all_tasks:
            results = await asyncio.gather(*all_tasks, return_exceptions=True)

            websocket_successful = sum(1 for result in results[: len(websocket_tasks)] if result is True)
            socketio_successful = (
                1 if socketio_task and len(results) > len(websocket_tasks) and results[-1] is True else 0
            )

            logger.info(
                f"Broadcast completed: {websocket_successful}/{len(websocket_tasks)} WebSocket, {socketio_successful}/1 Socket.IO"
            )

    async def _broadcast_to_socketio(self, message: Any):
        """
        Broadcast message to all Socket.IO clients

        Args:
            message: The message to broadcast
        """
        try:
            if self.socketio_server:
                # Convert message to appropriate format for Socket.IO
                if isinstance(message, dict):
                    await self.socketio_server.emit("analysis_progress", message)
                else:
                    await self.socketio_server.emit("analysis_progress", {"data": str(message)})
                return True
        except Exception as e:
            logger.error(f"Failed to broadcast to Socket.IO clients: {str(e)}")
            return False

    async def handle_message(self, connection_id: str, user_info: dict, message_data: dict):
        """
        Handle incoming message from a WebSocket client

        Args:
            connection_id: The connection ID that sent the message
            message_text: The received message text
            user_info: The user information associated with the connection
        Returns:
            dict: Response message to be sent back to the client
        """

        message_type = message_data.get("type", "unknown")

        logger.info(
            f"Handling message type '{message_type}' from connection {connection_id}, user = {user_info.get('email', 'unknown')}"
        )

        # Handle different message types
        if message_type == "ping":
            return {"type": "pong", "timestamp": asyncio.get_event_loop().time()}

        elif message_type == "echo":
            return {
                "type": "echo_response",
                "original_message": message_data,
                "timestamp": asyncio.get_event_loop().time(),
            }

        elif message_type == "broadcast_request":
            # Broadcast a message to all connected clients
            broadcast_message = {
                "type": "broadcast",
                "from_connection": connection_id,
                "message": message_data.get("message", "No message content"),
                "timestamp": asyncio.get_event_loop().time(),
            }
            await self.broadcast_to_all(broadcast_message)
            return {
                "type": "broadcast_sent",
                "message": "Broadcast message sent to all clients",
                "timestamp": asyncio.get_event_loop().time(),
            }

        elif message_type == WebSocketMessageType.SEND_QUERY.value:
            # Handle conversation requests using the generic orchestration method
            return await self._handle_send_query_request(connection_id, user_info, message_data)

        elif message_type == WebSocketMessageType.CREATE_CONVERSATION.value:
            # Handle conversation creation requests using the generic orchestration method
            return await self._handle_create_conversation_request(connection_id, user_info, message_data)

        else:
            # Default response for unknown message types
            return {
                "type": "response",
                "message": f"Received message of type: {message_type}",
                "original_message": message_data,
                "timestamp": asyncio.get_event_loop().time(),
            }

    async def _handle_send_query_request(self, connection_id: str, user_info: dict, message_data: dict):
        """
        Handle conversation request messages from WebSocket clients.

        Args:
            connection_id: The WebSocket connection ID
            message_data: The parsed message data
        Returns:
            dict: Response message to be sent back to the client
        """
        logger.info(f"Handling send_query request from connection {connection_id}, data:\n\n{message_data}\n\n")
        try:
            # Extract conversation request data from flat structure
            conversation_id = message_data.get("id")
            content = message_data.get("content", "")
            session_type = message_data.get("session_type", "analysis")
            uploads = message_data.get("uploads", [])
            is_file_upload = message_data.get("isFileUpload", False)
            reconnect = message_data.get("reconnect", False)
            insight_board_id = message_data.get("insight_board_id", "")
            session_id = message_data.get("sessionId", "")

            # Prepare request data in the format expected by orchestration agent
            request_data = {
                "content": content,
                "session_type": session_type,
                "uploads": uploads,
                "isFileUpload": is_file_upload,
                "reconnect": reconnect,
                "insight_board_id": insight_board_id,
                "reactionInfo": None,  # Will need to be extracted if available
                "dashboardInsightInfo": None,  # Will need to be extracted if available
            }

            # Import required services (avoid circular imports)
            from services.global_memory_service import GlobalMemoryService
            from services.conversation_service import ConversationService

            # Get service instances
            global_memory = GlobalMemoryService()
            conversation_service = ConversationService()

            # Use the generic method from orchestration agent (class variable)
            success, result, error_message = self.orchestration_agent.process_conversation_request(
                conversation_id=conversation_id,
                user_info=user_info,
                request_data=request_data,
                global_memory=global_memory,
                conversation_service=conversation_service,
            )

            if not success:
                # Return error response
                return {
                    "type": "conversation_error",
                    "conversation_id": conversation_id,
                    "error": error_message,
                    "timestamp": asyncio.get_event_loop().time(),
                }

            # Return success response
            return {
                "type": "conversation_started",
                "conversation_id": conversation_id,
                "timestamp": asyncio.get_event_loop().time(),
            }

        except Exception as e:
            logger.error(f"Error handling conversation request: {str(e)}")
            return {
                "type": "conversation_error",
                "conversation_id": conversation_id,
                "error": f"Internal server error: {str(e)}",
                "timestamp": asyncio.get_event_loop().time(),
            }

    async def _handle_create_conversation_request(self, connection_id: str, user_info: dict, message_data: dict):
        """
        Handle conversation creation request messages from WebSocket clients.

        Args:
            connection_id: The WebSocket connection ID
            message_data: The parsed message data
        Returns:
            dict: Response message to be sent back to the client
        """
        try:
            # Extract conversation creation request data from flat structure
            parent_conversation_id = message_data.get("parent_conversation_id", "")

            # Prepare request data in the format expected by orchestration agent
            request_data = {
                "parent_conversation_id": parent_conversation_id if parent_conversation_id else None,
            }

            # Import required services (avoid circular imports)
            from services.conversation_service import ConversationService

            # Get service instances
            conversation_service = ConversationService()

            # Use the generic method from orchestration agent (class variable)
            success, result, error_message = self.orchestration_agent.create_conversation(
                user_info=user_info,
                request_data=request_data,
                conversation_service=conversation_service,
            )

            if not success:
                # Return error response
                return {
                    "type": "conversation_error",
                    "error": error_message,
                    "timestamp": asyncio.get_event_loop().time(),
                }

            # Return success response
            return {
                "type": "create_conversation_success",
                "data": {
                    "conversation_id": result["conversationId"],
                },
                "timestamp": asyncio.get_event_loop().time(),
            }

        except Exception as e:
            logger.error(f"Error handling create conversation request: {str(e)}")
            return {
                "type": "conversation_error",
                "error": f"Internal server error: {str(e)}",
                "timestamp": asyncio.get_event_loop().time(),
            }

    def get_connection_count(self) -> int:
        """
        Get the number of active connections

        Returns:
            int: Number of active connections
        """
        return len(self.active_connections)

    def get_connection_ids(self) -> Set[str]:
        """
        Get all active connection IDs

        Returns:
            Set[str]: Set of active connection IDs
        """
        return self.connection_ids.copy()

    async def push_conversation_update(self, conversation_id: str, update_data: Dict[str, Any]):
        """
        Push conversation updates to all connected clients

        Args:
            conversation_id: The conversation ID
            update_data: The update data to send
        """
        message = {
            "type": "conversation_update",
            "conversation_id": conversation_id,
            "data": update_data,
            "timestamp": asyncio.get_event_loop().time(),
        }

        await self.broadcast_to_all(message)
        logger.info(f"Pushed conversation update for conversation {conversation_id} to all clients")

    async def push_final_answer_list(
        self, conversation_id: str, session_id: str, session_type: str, answer_list: list
    ):
        """
        Push final answer list to all connected clients

        Args:
            conversation_id: The conversation ID
            session_id: The session ID
            answer_list: The answer list to send
        """
        logger.info(f"Pushing final answer list for conversation {conversation_id}")
        ## Make the answer list JSON serializable
        # answer_list = self._make_json_compatible(answer_list)
        message = {
            "conversation_id": conversation_id,
            "session_id": session_id,
            "session_type": session_type,
            "data_type": WebSocketMessageType.ANALYSIS_RESULT.value,
            "data": {
                "type": WebSocketMessageType.ANALYSIS_RESULT.value,
                "data": answer_list,
            },
        }
        await self.multicast_to_authenticated_socketio_client(
            WebSocketMessageType.ANALYSIS_RESULT.value, message, conversation_id
        )
        logger.info(f"Pushed final answer list for conversation {conversation_id}")

    async def _stream_session_data(self, connection_id: str, session_instance):
        """
        Stream session data to the WebSocket connection.

        Args:
            connection_id: The WebSocket connection ID
            session_instance: The session instance to stream from
        """
        try:
            # Get the queue from the session instance
            queue = session_instance.dequeue()

            # Stream data from the queue
            async for chunk in queue:
                await self.send_to_connection(
                    connection_id,
                    {
                        "type": "conversation_chunk",
                        "data": chunk,
                        "timestamp": asyncio.get_event_loop().time(),
                    },
                )

        except Exception as e:
            logger.error(f"Error streaming session data: {str(e)}")
            await self.send_to_connection(
                connection_id,
                {
                    "type": "conversation_error",
                    "error": f"Streaming error: {str(e)}",
                    "timestamp": asyncio.get_event_loop().time(),
                },
            )

    def _make_json_compatible(self, data):
        """
        Make the data JSON serializable
        data: The data to make JSON serializable
            Eg: [{'block_id': '68a47ded0d28e828cca4403f.2000.0', 'block_type': <FrontendBlockType.MARKDOWN: ('MARKDOWN', 2000)>, 'status': 'completed', 'data': '**Welcome!**\n\nHow can I assist you today? Feel free to describe the business question or analysis you’d like me to tackle.', 'operation': 'add', 'block_tab_types': [<FrontendTabContentType.FINAL_CONCLUSION: ('final_conclusion', 'Final Conclusion')>]}]
        """
        from utils.constant import (
            FrontendBlockType,
            FrontendTabContentType,
        )
        import math

        try:
            from bson import ObjectId  # type: ignore
        except Exception:  # pragma: no cover - optional dependency at runtime
            ObjectId = None  # type: ignore

        def convert(obj):
            # None and primitives
            if obj is None or isinstance(obj, (str, bool, int)):
                return obj

            # Floats (handle NaN/Inf)
            if isinstance(obj, float):
                if math.isnan(obj) or math.isinf(obj):
                    return None
                return obj

            # Datetime
            if isinstance(obj, datetime):
                return obj.isoformat()

            # ObjectId (if available)
            if ObjectId is not None and isinstance(obj, ObjectId):  # type: ignore[arg-type]
                return str(obj)

            # Enums
            if isinstance(obj, Enum):
                # Specific handling for front-end facing enums
                if isinstance(obj, FrontendBlockType):
                    return obj.type_value
                if isinstance(obj, FrontendTabContentType):
                    return obj.tab_type_value

                # Generic enum handling
                value = obj.value
                # If enum value is a tuple like (identifier, ...), keep the identifier
                if isinstance(value, tuple) and len(value) > 0:
                    base = value[0]
                else:
                    base = value
                # Ensure recursively converted
                return convert(base)

            # Collections
            if isinstance(obj, dict):
                return {str(k): convert(v) for k, v in obj.items()}
            if isinstance(obj, (list, tuple, set)):
                return [convert(item) for item in obj]

            # Objects with to_dict
            if hasattr(obj, "to_dict") and callable(getattr(obj, "to_dict")):
                try:
                    return convert(obj.to_dict())
                except Exception:
                    return str(obj)

            # Fallback to string
            return str(obj)

        return convert(data)

    async def push_analysis_step_progress(
        self,
        conversation_id: str,
        session_id: str,
        session_type: str,
        question_title: str,
        current_step: str,
        progress: int = -1,
    ):
        """
        Push analysis step progress updates to all connected clients

        Args:
            conversation_id: The conversation ID (chat_id)
            session_id: The session ID
            session_type: The session type (e.g., 'complex_analysis')
            question_title: The title of the question being analyzed
            current_step: The current analysis step description
            progress: Progress percentage (default: -1, indicating not applicable)
        """
        message = {
            "conversation_id": conversation_id,
            "session_id": session_id,
            "status": "in_progress",
            "session_type": session_type,
            "data_type": WebSocketMessageType.ANALYSIS_STEP_PROGRESS.value,
            "data": {
                "type": WebSocketMessageType.ANALYSIS_STEP_PROGRESS.value,
                "data": {
                    "title": question_title,
                    "currentStep": current_step,
                    "isStreaming": True,
                },
            },
        }
        if progress >= 0:
            message["data"]["data"]["progress"] = progress

        # await self.broadcast_to_all(message)
        # await self.broadcast_to_authenticated_socketio_clients('progress_bar', message)
        await self.multicast_to_authenticated_socketio_client(
            WebSocketMessageType.ANALYSIS_STEP_PROGRESS.value, message, conversation_id
        )

        logger.info(f"Pushed analysis step progress for conversation {conversation_id}, step: {current_step}")

    async def push_user_question_received(
        self, conversation_id: str, session_id: str, session_type: str, user_question: str
    ):
        """
        Push user question received updates to all connected clients

        Args:
            conversation_id: The conversation ID (chat_id)
            session_id: The session ID
            session_type: The session type (e.g., 'complex_analysis')
            user_question: The user question received
        """
        message = {
            "conversation_id": conversation_id,
            "session_id": session_id,
            "status": "in_progress",
            "session_type": session_type,
            "data_type": "user_question_received",
            "data": {
                "type": "query",
                "data": user_question,
            },
        }
        await self.multicast_to_authenticated_socketio_client("query", message, session_id)
        # await self.broadcast_to_authenticated_socketio_clients('query', message)
        logger.info(f"Pushed user question received for conversation {conversation_id}")

    # Socket.IO Authentication Methods
    async def authenticate_socketio_connection(
        self, sid: str, environ: Dict[str, Any]
    ) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Authenticate a Socket.IO connection using bearer token

        Args:
            sid: Socket.IO session ID
            environ: The WSGI environment dictionary

        Returns:
            Tuple[bool, Optional[Dict], Optional[str]]: (is_authenticated, user_info, error_message)
        """
        try:
            # Use the authentication service to verify the connection
            is_authenticated, user_info, error = await self.socketio_auth_service.authenticate_connection(environ)

            if is_authenticated and user_info:
                # Store the authenticated session
                self.authenticated_socketio_sessions[sid] = user_info
                logger.info(f"Socket.IO session {sid} authenticated for user: {user_info.get('email', 'unknown')}")
                return True, user_info, None
            else:
                logger.warning(f"Socket.IO authentication failed for session {sid}: {error}")
                return False, None, error

        except Exception as e:
            logger.error(f"Error during Socket.IO authentication for session {sid}: {str(e)}")
            return False, None, f"Authentication error: {str(e)}"

    def get_authenticated_user(self, sid: str) -> Optional[Dict[str, Any]]:
        """
        Get user information for an authenticated Socket.IO session

        Args:
            sid: Socket.IO session ID

        Returns:
            Optional[Dict[str, Any]]: User information if authenticated, None otherwise
        """
        return self.authenticated_socketio_sessions.get(sid)

    def remove_authenticated_session(self, sid: str):
        """
        Remove an authenticated Socket.IO session

        Args:
            sid: Socket.IO session ID
        """
        if sid in self.authenticated_socketio_sessions:
            user_info = self.authenticated_socketio_sessions[sid]
            del self.authenticated_socketio_sessions[sid]
            logger.info(f"Removed authenticated Socket.IO session {sid} for user: {user_info.get('email', 'unknown')}")

    def get_authenticated_sessions_count(self) -> int:
        """
        Get the number of authenticated Socket.IO sessions

        Returns:
            int: Number of authenticated sessions
        """
        return len(self.authenticated_socketio_sessions)

    async def broadcast_to_authenticated_socketio_clients(
        self, event_type: str, data: Any, exclude_sid: Optional[str] = None
    ):
        """
        Broadcast a message to all authenticated Socket.IO clients

        Args:
            event: The event name to emit
            data: The data to send
            exclude_sid: Optional session ID to exclude from broadcast
        """
        if not self.socketio_server:
            logger.warning("Socket.IO server not available for broadcasting")
            return

        try:
            # Get all authenticated session IDs
            authenticated_sids = list(self.authenticated_socketio_sessions.keys())

            if exclude_sid:
                authenticated_sids = [sid for sid in authenticated_sids if sid != exclude_sid]

            # Broadcast to all authenticated clients
            for sid in authenticated_sids:
                await self.socketio_server.emit(event_type, data, to=sid)

            logger.info(f"Broadcasted '{event_type}' to {len(authenticated_sids)} authenticated Socket.IO clients")

        except Exception as e:
            logger.error(f"Error broadcasting to authenticated Socket.IO clients: {str(e)}")

    async def multicast_to_authenticated_socketio_client(self, event_type: str, data: Any, conversation_id: str):
        """
        Send a message to all authenticated Socket.IO clients connected to a specific conversation

        Args:
            event: The event name to emit
            data: The data to send
            conversation_id: The conversation ID of the target client
        """
        if not self.socketio_server:
            logger.warning("Socket.IO server not available for unicast")
            return

        # Get session IDs connected to the conversation
        sids = self.conversation_specific_socketio_sessions.get(conversation_id, set())

        try:
            # Send to all connected clients for the conversation
            for sid in sids:
                await self.socketio_server.emit(event_type, data, to=sid)
                logger.debug(
                    f"Multicasted '{event_type}' to Socket.IO client {sid}, conversation {conversation_id}, data: {data}"
                )
        except Exception as e:
            logger.error(f"Error Multicasting to Socket.IO clients for conversation {conversation_id}: {str(e)}")
