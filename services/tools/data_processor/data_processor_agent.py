import os
from models.conversation import Conversation
from services.shared.base_state_handler_agent import StateMachine
from services.tools.base_tool import BaseTool, ToolOutput
from utils.constant import AgentState, AnalysisStep
from utils.metalake import load_data
from services.shared.python_code_agent import PythonC<PERSON>Agent
from services.shared.code_review_agent import CodeReviewAgent
from services.shared.python_code_execution_agent import PythonExecuteAgent
from services.shared.textual_review_agent import TextualDataReviewAgent
from services.shared.visual_reviewer_agent import VisualReviewerAgent


class DataProcessorAgent(StateMachine, BaseTool):
    """
    Agentic Python workflow agent that manages the state transitions for solving a user question using Python code, code review, execution, and data/visual review.
    Input: user question + data file names list
    """

    def __init__(self):
        StateMachine.__init__(self)
        BaseTool.__init__(self)
        default_model = os.getenv("MODEL")
        # Register state handler classes
        self.state_handler_class_map[AgentState.PYTHON_CODE_GENERATION] = PythonCodeAgent
        self.state_handler_class_map[AgentState.CODE_REVIEW] = CodeReviewAgent
        self.state_handler_class_map[AgentState.PYTHON_CODE_EXECUTION] = PythonExecuteAgent
        self.state_handler_class_map[AgentState.TEXTUAL_REVIEW] = TextualDataReviewAgent
        self.state_handler_class_map[AgentState.VISUAL_REVIEW] = VisualReviewerAgent

        # Register state handler functions
        self.state_handler_function_map[AgentState.PYTHON_CODE_GENERATION] = self.handle_python_code_generation_output
        self.state_handler_function_map[AgentState.CODE_REVIEW] = self.handle_code_review_output
        self.state_handler_function_map[AgentState.PYTHON_CODE_EXECUTION] = self.handle_python_execution_output
        self.state_handler_function_map[AgentState.TEXTUAL_REVIEW] = self.handle_data_review_output
        self.state_handler_function_map[AgentState.VISUAL_REVIEW] = self.handle_visual_review_output

        # Set max invokes allowed for each state (tune as needed)
        self.max_invokes_allowed[AgentState.PYTHON_CODE_GENERATION] = (4, False)
        self.max_invokes_allowed[AgentState.CODE_REVIEW] = (100, False)
        self.max_invokes_allowed[AgentState.PYTHON_CODE_EXECUTION] = (100, False)
        self.max_invokes_allowed[AgentState.TEXTUAL_REVIEW] = (100, False)
        self.max_invokes_allowed[AgentState.VISUAL_REVIEW] = (2, False)
        self.default_model = default_model

    def handle_python_code_generation_output(self, output_dict, conversation, chat_log, is_last_attempt):
        # After Python code generation, move to code review
        next_input = {
            "user_question": conversation.user_inputs[-1],
            "python_code": output_dict["code"],
            "reasoning": output_dict.get("logic", ""),
            "data_input": output_dict.get("data_input", ""),
            "reusable_variables": conversation.global_var_list,
            "file_paths": output_dict.get("file_paths", {}),
        }
        return AgentState.CODE_REVIEW, next_input

    def handle_code_review_output(self, output_dict, conversation, chat_log, is_last_attempt):
        # If revision is required, go back to Python code generation
        if output_dict.get("is_revision_required", False):
            chat_log.info("PythonAgent | handle_code_review_output | Code review failed. Revising Python code")
            next_input = {"review_feedback": output_dict.get("review_feedback", "")}
            return AgentState.PYTHON_CODE_GENERATION, next_input
        # If no revision is required, move to Python execution
        python_code = output_dict.get("python_code", "")
        next_input = {
            "user_question": conversation.user_inputs[-1],
            "python_code": python_code,
            "file_paths": output_dict.get("file_paths", {}),
            "output_data_files": output_dict.get("output_data_files", []),
        }
        return AgentState.PYTHON_CODE_EXECUTION, next_input

    def handle_python_execution_output(self, output_dict, conversation, chat_log, is_last_attempt):
        # If execution failed, go back to Python code generation
        if not output_dict.get("execution_success", False):
            chat_log.info("PythonAgent | handle_python_execution_output | Execution failed. Revising Python code")
            next_input = {"execution_error": output_dict.get("execution_error", "Unknown execution error")}
            return AgentState.PYTHON_CODE_GENERATION, next_input
        # Move to data review
        conversation.current_question_raw_answer = str(output_dict.get("execution_result", ""))
        return AgentState.TEXTUAL_REVIEW, output_dict

    def handle_data_review_output(self, output_dict, conversation, chat_log, is_last_attempt):
        # If revision is required, go back to Python code generation
        if output_dict.get("is_revision_required", False):
            chat_log.info("PythonAgent | handle_data_review_output | Data review failed. Revising Python code")
            next_input = {
                "review_feedback": output_dict.get("feedback", ""),
                "execution_result": output_dict.get("data_observations", ""),
            }
            return AgentState.PYTHON_CODE_GENERATION, next_input
        # If there are image files to review, move to visual review
        if output_dict.get("image_file_paths", []):
            self.state_handler_instance_map[AgentState.PYTHON_CODE_GENERATION].clear_history(conversation, True)
            self.state_handler_instance_map[AgentState.PYTHON_CODE_EXECUTION].clear_history(conversation)
            chat_log.info("PythonAgent | handle_data_review_output | Moving to visual review")
            next_input = {
                "user_question": conversation.user_inputs[-1],
                "python_code": output_dict.get("python_code", ""),
                "execution_result": output_dict.get("execution_result", ""),
                "image_file_paths": output_dict.get("image_file_paths", []),
                "csv_paths": output_dict.get("csv_paths", []),
                "output_data_files": output_dict.get("output_data_files", []),
            }
            return AgentState.VISUAL_REVIEW, next_input
        # If no revision is required and no images to review, mark as completed
        conversation.agent_state = AgentState.COMPLETED
        return AgentState.COMPLETED, output_dict

    def handle_visual_review_output(self, output_dict, conversation, chat_log, is_last_attempt):
        # If revision is required, go back to Python code generation - except in the last attempt
        if output_dict.get("is_revision_required", False) and not is_last_attempt:
            chat_log.info("PythonAgent | handle_visual_review_output | Visual review failed. Revising Python code")
            next_input = {
                "review_feedback": output_dict.get("feedback", ""),
                "is_visualization_issue": output_dict.get("is_visualization_issue", False),
            }
            # Store feedback in conversation history if it's a visualization issue
            if output_dict.get("is_visualization_issue", False):
                if not hasattr(conversation, "data_reviewer_feedback_history"):
                    conversation.data_reviewer_feedback_history = []
                conversation.data_reviewer_feedback_history.append(
                    {
                        "feedback": output_dict.get("feedback", ""),
                        "is_visualization_issue": True,
                        "issue_severity": output_dict.get("issue_severity", ""),
                    }
                )
            return AgentState.PYTHON_CODE_GENERATION, next_input
        # If no revision is required, mark as completed
        conversation.agent_state = AgentState.COMPLETED
        return AgentState.COMPLETED, output_dict

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
        event_loop=None,
    ):
        # Take current cycle index from additional_data if available
        current_cycle = additional_data.get("current_cycle", 0) if additional_data else 0
        conversation.active_session_send_analysis_step(AnalysisStep.DATA_PROCESSING, current_cycle=current_cycle)
        # Load first 5 records from given data files and give as additional input to Python code agent
        # First check the existance of all data files and inform back if any of them are missing
        non_existing_files_list = []
        for data_file_name in data_file_name_list:
            data_file_path = f"storage/public/{conversation.chat_id}/{data_file_name}.pkl"
            if not os.path.exists(data_file_path):
                non_existing_files_list.append(data_file_name)
        if non_existing_files_list:
            return ToolOutput(
                display_output=f"Failed to find following mentioned files: {non_existing_files_list}\n\nPlease check the file names and try again.\n\n",
                result_text_list=[tuple([f"Files not found: {non_existing_files_list}", False])],
            )

        preview_data_str = ""
        for data_file_name in data_file_name_list:
            data_records = load_data(conversation.chat_id, data_file_name)
            if not data_records.empty:
                preview_data_str += (
                    "\n\nData file name: "
                    + data_file_name
                    + ": First 5 rows:\n\n"
                    + data_records.head(5).to_markdown()
                    + "\n\n"
                )
        input_data = {
            "user_question": task_instruction,
            "data_file_names": data_file_name_list,
            "data_input": preview_data_str,
        }
        # Set the correct agent state to start with
        conversation.agent_state = AgentState.PYTHON_CODE_GENERATION
        output_dict = self.start(
            conversation,
            data_source_name,
            input_data,
            base_log_path,
        )
        # Get output CSV and image file names
        csv_file_names = []
        image_file_names = []
        for file_path in output_dict.get("csv_paths", []):
            # check if the extension is csv, if not then skip
            if not file_path.endswith(".csv"):
                continue
            csv_file_names.append(file_path.split("/")[-1])
        for file_path in output_dict.get("image_file_paths", []):
            image_file_names.append(file_path.split("/")[-1])
        tool_result_to_summarize = (
            "Python code:\n\n```python\n"
            + output_dict.get("python_code", "")
            + "\n````\n\nOutput:\n\n"
            + output_dict.get("execution_result", "")
            + "\n\n"
        )
        tool_result_to_not_summarize = (
            "Output data files: "
            + str(output_dict.get("output_data_files", []))
            + "\nOutput csv files: "
            + str(csv_file_names)
            + "\nOutput image files: "
            + str(image_file_names)
            + "\n"
        )
        tool_results_list = [
            tuple([tool_result_to_summarize, True]),
            tuple([tool_result_to_not_summarize, False]),
        ]

        output_display = (
            "Result: "
            + output_dict.get("execution_result", "")
            + "\n\n"
            + "Output data files: "
            + str(output_dict.get("output_data_files", []))
            + "\n\n"
            + "Output CSV files: "
            + str(csv_file_names)
            + "\n\n"
            + "Output image files: "
            + str(image_file_names)
        )
        return ToolOutput(display_output=output_display, result_text_list=tool_results_list)
