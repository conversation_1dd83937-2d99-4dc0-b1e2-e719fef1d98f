"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class TableDataProcessorAgent
* @description Tool responsible for processing tabular data using Python code.
* <AUTHOR>
"""

import os
from models.conversation import Conversation
from services.tools.data_processor.data_processor_agent import DataProcessorAgent
from utils.constant import AgentState, AnalysisStep
from utils.metalake import load_data
from services.shared.python_code_agent import TableProcessorCodeAgent

from services.tools.base_tool import ToolOutput


class TableDataProcessorAgent(DataProcessorAgent):
    """
    Agentic Python workflow agent that manages the state transitions for solving a user question using Python code, code review, execution, and data/visual review.
    Input: user question + data file names list
    """

    def __init__(self):
        super().__init__()
        # Register state handler classes
        self.state_handler_class_map[AgentState.PYTHON_CODE_GENERATION] = TableProcessorCodeAgent

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
        event_loop=None,
    ):
        conversation.active_session_send_analysis_step(AnalysisStep.TABLE_DATA_PROCESSING)
        # Load first 5 records from given data files and give as additional input to Python code agent
        # First check the existance of all data files and inform back if any of them are missing
        non_existing_files_list = []
        for data_file_name in data_file_name_list:
            data_file_path = f"storage/public/{conversation.chat_id}/{data_file_name}.pkl"
            if not os.path.exists(data_file_path):
                non_existing_files_list.append(data_file_name)
        if non_existing_files_list:
            return ToolOutput(
                display_output=f"Failed to find following mentioned files: {non_existing_files_list}\n\nPlease check the file names and try again.\n\n",
                result_text_list=[tuple([f"Files not found: {non_existing_files_list}", False])],
            )

        preview_data_str = ""
        for data_file_name in data_file_name_list:
            data_records = load_data(conversation.chat_id, data_file_name)
            if not data_records.empty:
                preview_data_str += (
                    "\n\nData file name: "
                    + data_file_name
                    + ": First 5 rows:\n\n"
                    + data_records.head(5).to_markdown()
                    + "\n\n"
                )
        input_data = {
            "user_question": task_instruction,
            "data_file_names": data_file_name_list,
            "data_input": preview_data_str,
        }
        # Set the correct agent state to start with
        conversation.agent_state = AgentState.PYTHON_CODE_GENERATION
        output_dict = self.start(
            conversation,
            data_source_name,
            input_data,
            base_log_path,
        )
        # Get output CSV file names
        csv_file_names = []
        for file_path in output_dict.get("csv_paths", []):
            # check if the extension is csv, if not then skip
            if not file_path.endswith(".csv"):
                continue
            csv_file_names.append(file_path.split("/")[-1])
        tool_result_to_summarize = (
            "Python code:\n\n```python\n"
            + output_dict.get("python_code", "")
            + "\n````\n\nOutput:\n\n"
            + output_dict.get("execution_result", "")
            + "\n\n"
        )
        tool_result_to_not_summarize = (
            "Output data files: "
            + str(output_dict.get("output_data_files", []))
            + "\nOutput csv files: "
            + str(csv_file_names)
            + "\n"
        )
        tool_results_list = [
            tuple([tool_result_to_summarize, True]),
            tuple([tool_result_to_not_summarize, False]),
        ]

        output_display = (
            "Result: "
            + output_dict.get("execution_result", "")
            + "\n\n"
            + "Output data files: "
            + str(output_dict.get("output_data_files", []))
            + "\n\n"
            + "Output CSV files: "
            + str(csv_file_names)
        )
        return ToolOutput(display_output=output_display, result_text_list=tool_results_list)
