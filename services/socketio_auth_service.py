"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* socketio_auth_service.py handles Socket.IO authentication using bearer tokens

* @class SocketIOAuthService
* @description This class manages Socket.IO authentication and token verification
* <AUTHOR> Assistant
"""

import json
from typing import Optional, Dict, Any, Tuple
from services.custom_token_service import verify_token
from utils.logger import get_debug_logger

logger = get_debug_logger("socketio_auth_service", "./logs/socketio_auth.log")


class SocketIOAuthService:
    """
    Service class to handle Socket.IO authentication using bearer tokens
    """

    def __init__(self):
        logger.info("SocketIOAuthService initialized")

    async def extract_token_from_handshake(self, environ: Dict[str, Any]) -> Optional[str]:
        """
        Extract bearer token from Socket.IO handshake request
        
        Args:
            environ: The WSGI environment dictionary
            
        Returns:
            Optional[str]: The extracted token or None if not found
        """
        try:
            # Check for token in query parameters
            query_string = environ.get('QUERY_STRING', '')
            if query_string:
                import urllib.parse
                query_params = urllib.parse.parse_qs(query_string)
                token = query_params.get('token', [None])[0]
                if token:
                    logger.info("Token found in query parameters")
                    return token

            # Check for token in headers
            headers = environ.get('HTTP_AUTHORIZATION', '')
            if headers.startswith('Bearer '):
                token = headers[7:]  # Remove 'Bearer ' prefix
                logger.info("Token found in Authorization header")
                return token

            # Check for token in custom header
            custom_auth_header = environ.get('HTTP_X_AUTH_TOKEN', '')
            if custom_auth_header:
                logger.info("Token found in X-Auth-Token header")
                return custom_auth_header

            logger.warning("No authentication token found in handshake")
            return None

        except Exception as e:
            logger.error(f"Error extracting token from handshake: {str(e)}")
            return None

    async def verify_token(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Verify the provided bearer token
        
        Args:
            token: The bearer token to verify
            
        Returns:
            Tuple[bool, Optional[Dict], Optional[str]]: (is_valid, user_info, error_message)
        """
        try:
            if not token:
                return False, None, "No token provided"

            # Verify token using existing token service
            user_info, error = await verify_token(token)
            
            if error:
                logger.warning(f"Token verification failed: {error}")
                return False, None, error
            
            if not user_info:
                logger.warning("Token verification returned no user info")
                return False, None, "Invalid token"

            logger.info(f"Token verified successfully for user: {user_info.get('email', 'unknown')}")
            return True, user_info, None

        except Exception as e:
            logger.error(f"Error during token verification: {str(e)}")
            return False, None, f"Token verification error: {str(e)}"

    async def authenticate_connection(self, environ: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Authenticate a Socket.IO connection using bearer token
        
        Args:
            environ: The WSGI environment dictionary
            
        Returns:
            Tuple[bool, Optional[Dict], Optional[str]]: (is_authenticated, user_info, error_message)
        """
        try:
            # Extract token from handshake
            token = await self.extract_token_from_handshake(environ)
            
            if not token:
                return False, None, "No authentication token provided"

            # Verify the token
            is_valid, user_info, error = await self.verify_token(token)
            
            if not is_valid:
                return False, None, error or "Token verification failed"

            return True, user_info, None

        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return False, None, f"Authentication error: {str(e)}" 