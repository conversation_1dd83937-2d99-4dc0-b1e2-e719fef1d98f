"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* UserService use for handle services related to User

* @class UserService
* @description This class use for handle services related to User
* <AUTHOR>
"""

from datetime import datetime, timezone
import json
import os
import pathlib

from bson import ObjectId
from databases.mongo_manager import MongoDBmanager
from utils.llm_utils import get_dict_from_json_or_python
from services.llm_client_factory import LLMClientFactory
from models.schemas.responses import GeneralResponse
from utils.metalake import MetaLake
from utils.llm_client import LlmClient
from utils.logger import get_debug_logger


if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))
server_logger = get_debug_logger(
    "user_service", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log")
)

BUSINESS_OVERVIEW = "overview_business.txt"
DATA_SOURCES_OVERVIEW = "overview.json"


class UserService:
    def __init__(self, user_id):
        self.user_collection = MongoDBmanager("Users")
        self.logger = server_logger
        self.user_id = user_id
        self.company = os.getenv("COMPANY")
        self.messages_collection = MongoDBmanager("Messages")
        self.llm_model_name = os.getenv("MODEL")
        self.llm_client_factory = LLMClientFactory()
        self.llm_client = self.llm_client_factory.get_llm_client(
            os.getenv("LLM_API_PROVIDER"), os.getenv("TEMPERATURE")
        )

    def get_last_questions(self, limit=10):
        """
        This method retrieves the last asked questions by the user.

        Args:
            limit (int): The number of questions to retrieve. Defaults to 10.

        Returns:
            list: A list of strings containing the last asked questions.
        """
        message_query = {"userId": ObjectId(self.user_id), "senderType": "user", "isInitialQuestion": True}
        sort_criteria = [("createdAt", -1)]
        field_filter = {"_id": 0, "content": 1}

        messages = self.messages_collection.get_documents(message_query, sort_criteria, limit, field_filter)

        if messages is None or len(messages) == 0:
            return []

        messages = [message["content"] for message in messages]

        return messages

    def generate_suggested_questions(self, question):
        """
        This method generates suggested questions based on the given question,business overview,data sources overview and previous questions asked by user.

        Args:
            question (str): The question asked by user.

        Returns:
            None
        """
        try:
            business_overview = ""
            with open(f"data_dictionaries/{self.company}/" + BUSINESS_OVERVIEW, "r", encoding="utf-8") as file:
                business_overview = str(file.read())

            data_sources_overview = ""
            with open(f"data_dictionaries/{self.company}/" + DATA_SOURCES_OVERVIEW, "r", encoding="utf-8") as file:
                data_sources_overview = str(json.load(file))

            latest_message_list = self.get_last_questions()

            suggest_question_instructions = ""
            with open(f"instructions/instructions_generate_suggested_questions.txt", "r", encoding="utf-8") as file:
                suggest_question_instructions = str(file.read())

            suggest_question_instructions = suggest_question_instructions.replace(
                "<<BUSINESS_OVERVIEW>>", business_overview
            )
            suggest_question_instructions = suggest_question_instructions.replace(
                "<<DATA_SOURCES_OVERVIEW>>", data_sources_overview
            )
            suggest_question_instructions = suggest_question_instructions.replace(
                "<<PREVIOUS_QUESTIONS>>", str(latest_message_list)
            )
            suggest_question_instructions = suggest_question_instructions.replace("<<MOST_RECENT_QUESTION>>", question)

            llm_prompt = [{"role": "system", "content": str(suggest_question_instructions)}]

            response_schema = {
                "name": "suggested_questions",
                "strict": True,
                "schema": {
                    "type": "object",
                    "properties": {
                        "questions": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "description": "A single question as a string.",
                            },
                            "description": "An array of questions.",
                        }
                    },
                    "required": ["questions"],
                    "additionalProperties": False,
                },
            }

            llm_response = self.llm_client.get_llm_response(
                self,
                None,  # conversation
                self.logger,
                message_list=llm_prompt,
                model=self.llm_model_name,
                response_format=response_schema,
            )

            llm_response = llm_response.choices[0].message.content

            message_list = get_dict_from_json_or_python(llm_response).get("questions", [])

            if message_list is None or len(message_list) == 0:
                self.logger.warn(
                    f"UserService.generate_suggested_questions | Question : {question} | No suggested questions generated."
                )
                return

            # save messages in to user collection
            find_query = {"userId": ObjectId(self.user_id)}
            update_query = {"suggestedQuestions": message_list, "updatedAt": datetime.now(timezone.utc)}
            self.user_collection.upsert(find_query, update_query, None)

            self.logger.info(
                f"UserService.generate_suggested_questions | Question : {question} | Suggested questions generated and saved."
            )

        except Exception as e:
            self.logger.error(f"UserService.generate_suggested_questions | Question : {question} | Error: {str(e)}")
            return

    def get_suggested_questions(self, limit=5):
        """
        Retrieves suggested questions for the user.

        Parameters:
            limit (int): The number of questions to retrieve. Defaults to 5.

        Returns:
            GeneralResponse: A GeneralResponse containing a list of suggested questions if the operation was successful.
        """
        try:
            find_query = {"userId": ObjectId(self.user_id)}
            field_filter = {"_id": 0, "suggestedQuestions": 1}
            suggested_questions = self.user_collection.get_one_document(
                query=find_query, sort_criteria_list=[], field_filter=field_filter
            )

            # if suggested questions are available for user return them
            if (
                suggested_questions
                and "suggestedQuestions" in suggested_questions
                and suggested_questions.get("suggestedQuestions", [])
            ):
                _data = suggested_questions.get("suggestedQuestions", [])[:limit]

                # formatting for frontend
                data = [{"text": question} for question in _data]
                return GeneralResponse(success=True, data={"suggested_questions": data})

            # if suggested questions are not available for user, use suggested questions from metalake system wide questions
            metalake_client = MetaLake()
            suggested_questions_metalake = metalake_client.layernext_client.retrieve_suggested_questions()

            if suggested_questions_metalake and suggested_questions_metalake.get("suggestedQuestions", []):
                _data = suggested_questions_metalake.get("suggestedQuestions", [])[:limit]

                # formatting for frontend
                data = [{"text": question} for question in _data]
                return GeneralResponse(success=True, data={"suggested_questions": data})
            else:
                return GeneralResponse(success=True, data={"suggested_questions": []})

        except Exception as e:
            self.logger.error(f"UserService.get_suggested_questions | Error: {str(e)}")
            return GeneralResponse(success=False, message=str(e))
