"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class OrchestrationAgent
* @description This class is the entry point of data retrieval from SQL and non-SQL data sources
* <AUTHOR>
"""

from datetime import datetime, timedelta, timezone
from logging import Logger
import os
import pathlib
import traceback
from typing import Dict, Any, Optional, Tuple

from services.insight_dashboard_service import InsightDashboardService
from services.conversation_service import ConversationService
from models.schemas.responses import GeneralResponse
from services.insight_app.insight_generator_service import LlmInsightAgent
from services.shared.base_state_handler_agent import StateMachine
from services.tools.data_finder.data_finder_agent import DataFinderAgent
from services.upload_session_service import UploadSessionService
from services.tools.knowledge_finder.knowledge_generator_agent import KnowledgeGeneratorAgent
from utils.logger import get_debug_logger
from utils.constant import (
    ConversationConclusion,
    ConversationStatus,
    DataBlockHandlerType,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    LLMAgentType,
    SectionType,
    AgentState,
    SessionType,
    ConversationType,
    ConversationLLMStatus,
    UserReaction,
    ChatAppUserMessages,
    MAX_SESSIONS_PER_CONVERSATION,
)
from services.sql_agent.sql_agent import SQLAgent
from models.conversation import Conversation
from models.session import Session
from services.orchestration_agent.followup_handler_agent import FollowupHandlerAgent
from services.orchestration_agent.intent_router_agent import IntentRouterAgent
from services.orchestration_agent.answer_prepare_agent import AnswerPrepareAgent
from services.shared.python_code_agent import PythonCodeAgent
from services.shared.python_code_execution_agent import PythonExecuteAgent
from services.shared.code_review_agent import CodeReviewAgent
from services.shared.textual_review_agent import TextualDataReviewAgent
from services.shared.visual_reviewer_agent import VisualReviewerAgent
from services.shared.metadata_agent import MetadataAgent
from services.shared.iterative_problem_solver_agent import IterativeProblemSolverAgent
from services.global_memory_service import GlobalMemoryService
from services.insight_app.insight_report_service import InsightReportService
from services.user_service import UserService
import concurrent.futures

# Load environment variables
GENERATE_QUESTION_SUGGESTIONS_FOR_USER = os.getenv("GENERATE_QUESTION_SUGGESTIONS_FOR_USER", "disabled")


class OrchestrationAgent(StateMachine):
    """
    This class is responsible for:
    1. Handling followups - combine the previous conversation history with the current prompt
    2. Handling pre-verified questions - if the question is exactly a one of previously verified question, then directly execute the python code and answer
    3. Sorting of question - whether it's a) out-of the scope one, b) greeting message, c) SQL question, d) non-SQL question, e) hybrid question (SQL + non-SQL)
    4. Identify the steps of data retrievals
    5. Invoke appropriate agents for each step
    6. Collect the results and present the final answer to the user
    """

    def __init__(self):
        """
        Initialize the OrchestrationAgent with necessary configurations
        """
        print("Initializing OrchestrationAgent")
        super().__init__()
        default_model = os.getenv("MODEL")
        self.followup_handler = FollowupHandlerAgent(default_model)
        self.tool_router = IntentRouterAgent(default_model)
        self.answer_prepare = AnswerPrepareAgent("", default_model)
        self.upload_session_service = UploadSessionService()
        self.conversation_service = ConversationService()

        # Email Time Threshold
        self.email_time_threshold = int(os.getenv("ANALYSIS_EMAIL_TIME_THRESHOLD", 300))

        # Model configurations
        self.orchestration_model = os.getenv("ORCHESTRATION_MODEL", "gpt-4o")

        # Initialize state handlers for Python code generation and execution
        self.state_handler_class_map[AgentState.PYTHON_CODE_GENERATION] = PythonCodeAgent
        self.state_handler_class_map[AgentState.PYTHON_CODE_EXECUTION] = PythonExecuteAgent
        self.state_handler_class_map[AgentState.CODE_REVIEW] = CodeReviewAgent
        self.state_handler_class_map[AgentState.TEXTUAL_REVIEW] = TextualDataReviewAgent
        self.state_handler_class_map[AgentState.VISUAL_REVIEW] = VisualReviewerAgent
        self.state_handler_class_map[AgentState.METADATA_QUERY] = MetadataAgent
        self.state_handler_class_map[AgentState.COMPLEX_REASONING] = IterativeProblemSolverAgent
        self.state_handler_class_map[AgentState.KNOWLEDGE_MANAGER] = KnowledgeGeneratorAgent

        # Functions to handle the output of each state
        self.state_handler_function_map[AgentState.PYTHON_CODE_GENERATION] = self.handle_python_code_generation_output
        self.state_handler_function_map[AgentState.PYTHON_CODE_EXECUTION] = self.handle_python_execution_output
        self.state_handler_function_map[AgentState.CODE_REVIEW] = self.handle_code_review_output
        self.state_handler_function_map[AgentState.TEXTUAL_REVIEW] = self.handle_data_review_output
        self.state_handler_function_map[AgentState.VISUAL_REVIEW] = self.handle_visual_review_output
        self.state_handler_function_map[AgentState.METADATA_QUERY] = self.handle_metadata_query_output
        self.state_handler_function_map[AgentState.COMPLEX_REASONING] = self.handle_complex_reasoning_output
        self.state_handler_function_map[AgentState.KNOWLEDGE_MANAGER] = self.handle_knowledge_manager_output

        # No. of max. invokes allowed for each state with whether we ask back from user on limit reached
        self.max_invokes_allowed[AgentState.PYTHON_CODE_GENERATION] = (5, False)
        self.max_invokes_allowed[AgentState.PYTHON_CODE_EXECUTION] = (4, False)
        self.max_invokes_allowed[AgentState.CODE_REVIEW] = (3, False)
        self.max_invokes_allowed[AgentState.TEXTUAL_REVIEW] = (3, False)
        self.max_invokes_allowed[AgentState.VISUAL_REVIEW] = (2, False)
        self.max_invokes_allowed[AgentState.METADATA_QUERY] = (2, False)
        self.max_invokes_allowed[AgentState.COMPLEX_REASONING] = (1, False)
        self.max_invokes_allowed[AgentState.KNOWLEDGE_MANAGER] = (1, False)

        self.data_finder_agent = DataFinderAgent()
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        self.insight_report_service = InsightReportService()

    def process_conversation_request(
        self,
        conversation_id: str,
        user_info: dict,
        request_data: Dict[str, Any],
        global_memory: GlobalMemoryService,
        conversation_service: ConversationService,
    ) -> Tuple[bool, Any, Optional[str]]:
        """
        Generic method to process conversation requests that can be used by both REST API and WebSocket services.

        This method encapsulates the core logic from the chat controller's llm_request method,
        making it reusable across different transport layers.

        Args:
            conversation_id: The conversation ID
            user_info: User information dictionary
            request_data: Request data containing content, session_type, uploads, etc.
            global_memory: Global memory service instance
            conversation_service: Conversation service instance

        Returns:
            Tuple of (success: bool, result: Any, error_message: Optional[str])
        """
        try:
            logger = get_debug_logger(f"orchestration_{conversation_id}", f"./logs/chat_{conversation_id}.log")
            # Validate user info
            if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
                return False, None, ChatAppUserMessages.TEAM_NOT_EXIST.value

            # Validate conversation ID
            if not conversation_id or conversation_id == "":
                return False, None, ChatAppUserMessages.CONVERSATION_ID_REQUIRED.value

            # Get conversation data from database
            conversation_db_obj = conversation_service.get_conversation_data(conversation_id)
            if not conversation_db_obj:
                return False, None, ChatAppUserMessages.CHAT_CAN_NOT_FIND.value

            # Check if conversation is deleted
            is_deleted = conversation_db_obj.get("status", "") == ConversationStatus.DELETED.value
            if is_deleted:
                return False, None, ChatAppUserMessages.CHAT_DELETED.value

            # Handle reconnection logic
            reconnect = request_data.get("reconnect", False)
            if reconnect or conversation_db_obj.get("status") == ConversationStatus.IN_PROGRESS.value:
                if conversation_id not in global_memory.active_conversations:
                    return False, None, ChatAppUserMessages.CHAT_CAN_NOT_RECONNECT_CONVERSATION_NOT_IN_MEMORY.value
                # Return reconnection signal
                return True, {"type": "reconnect", "conversation_id": conversation_id}, None

            # Check session limit
            curr_session_count = len(conversation_db_obj.get("sessionIdList", []))
            if curr_session_count >= MAX_SESSIONS_PER_CONVERSATION:
                return False, None, ChatAppUserMessages.SESSION_LIMIT_EXCEEDED.value

            # Handle insight board attachment
            insight_board_id = request_data.get("insight_board_id")
            if (
                conversation_db_obj.get("status") == ConversationStatus.QUEUED.value
                and insight_board_id is not None
                and insight_board_id != ""
            ):
                conversation_service.attach_conversation_as_insight_followup(
                    insight_board_id, conversation_id, request_data.get("content", "")
                )

            # Extract and process request data
            prompt = request_data.get("content", "").strip()
            request_session_type = request_data.get("session_type")
            uploads = request_data.get("uploads", [])
            is_file_upload = request_data.get("isFileUpload", False)
            reaction_info = request_data.get("reactionInfo")
            dashboard_insight_info = request_data.get("dashboardInsightInfo")
            logger.info(f"OrchestrationAgent.process_conversation_request | Prompt: {prompt}, uploads: {uploads}")

            # Determine agent type
            agent_type = self._determine_agent_type(conversation_db_obj, prompt)
            if agent_type is None:
                return False, None, ChatAppUserMessages.INVALID_CONVERSATION_TYPE.value

            # Process special prompts
            prompt, agent_type, is_user_feedback = self._process_special_prompts(
                prompt, agent_type, conversation_id, user_info, conversation_service
            )

            # Handle session type specific logic
            user_question = self._handle_session_type_logic(
                request_session_type,
                prompt,
                conversation_id,
                reaction_info,
                dashboard_insight_info,
                conversation_service,
            )

            # Create session
            user_id = user_info["id"]
            session_db_res = conversation_service.initialize_session_in_db(
                conversation_id, request_session_type, user_id, user_question
            )
            if not session_db_res.success:
                return False, None, session_db_res.message

            session_id = session_db_res.data
            request_session_type_enum = self._get_session_type_enum(request_session_type)

            # Create session and conversation instances
            session_instance = Session(conversation_id, session_id, session_type=request_session_type_enum)
            history_messages = conversation_service.load_message_history(conversation_id, user_id)

            conversation_instance = Conversation(
                conversation_id,
                session_id,
                user_question,
                user_id,
                len(history_messages) > 0,
                agent_type,
                uploads,
            )

            # Setup conversation instance
            self._setup_conversation_instance(
                conversation_instance,
                session_instance,
                conversation_db_obj,
                history_messages,
                user_info,
                request_session_type_enum,
                dashboard_insight_info,
                is_user_feedback,
                global_memory,
            )

            # Update conversation in database
            conversation_service.update_conversation(conversation_instance, session_id, user_id)

            # Handle file upload case
            if is_file_upload and not prompt:
                self._handle_file_upload_case(conversation_instance)

            # Generate question suggestions if enabled
            if GENERATE_QUESTION_SUGGESTIONS_FOR_USER == "enabled":
                self._generate_question_suggestions(prompt, user_info["id"])

            # Start orchestration
            base_log_path = ""
            self.thread_pool.submit(self.run, conversation_instance, base_log_path, user_info)

            return (
                True,
                {
                    "type": "streaming_response",
                    "session_instance": session_instance,
                    "conversation_instance": conversation_instance,
                },
                None,
            )

        except Exception as e:
            return False, None, str(e)

    def _determine_agent_type(self, conversation_db_obj: dict, prompt: str) -> Optional[LLMAgentType]:
        """Determine the agent type based on conversation data and prompt."""
        raw_agent_type = conversation_db_obj.get("type", LLMAgentType.AGENT_TYPE_CHAT.value)

        # Check if raw_agent_type is a valid LLMAgentType value
        if any(raw_agent_type == member.value for member in LLMAgentType):
            return LLMAgentType(raw_agent_type)
        # Handle legacy ConversationType values
        elif raw_agent_type == ConversationType.INSIGHT_GEN_CONVERSATION.value:
            return LLMAgentType.AGENT_TYPE_INSIGHT
        elif raw_agent_type == ConversationType.CHAT_BOT_CONVERSATION.value:
            return LLMAgentType(conversation_db_obj.get("agent_type", LLMAgentType.AGENT_TYPE_CHAT.value))
        elif raw_agent_type == ConversationType.INSIGHT_FOLLOW_UP_CONVERSATION.value:
            return LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP
        # Default case
        else:
            return LLMAgentType.AGENT_TYPE_CHAT

    def _process_special_prompts(
        self,
        prompt: str,
        agent_type: LLMAgentType,
        conversation_id: str,
        user_info: dict,
        conversation_service: ConversationService,
    ) -> Tuple[str, LLMAgentType, bool]:
        """Process special prompts like @insight, @feedback, etc."""
        is_user_feedback = False
        user_question = prompt

        # @insight not allowed for insight follow up
        if agent_type == LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP:
            if prompt.lower().startswith("@insight"):
                raise ValueError("New Insight not allowed for insight follow up")

        # Handle insight prompt
        if prompt.lower().startswith("@insight"):
            conversation_type = ConversationType.INSIGHT_GEN_CONVERSATION.value
            conversation_service.set_conversation_type(conversation_id, conversation_type)
            agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
            prompt = {"query": user_question, "initial_data": ""}
            insight_report_id = self.insight_report_service.create_insight_report_draft(
                insight_id=conversation_id,
                userId=user_info["id"],
                user_email=user_info["email"],
                query=prompt["query"],
            )
        # Handle feedback prompt
        elif prompt.lower().startswith("@feedback"):
            is_user_feedback = True
        # For testing: Handle insight_feedback prompt
        elif prompt.lower().startswith("@thought_feedback"):
            prompt = prompt.replace("@thought_feedback", "")
            hypothesis_id, thought_index, comment = prompt.split(":")
            conversation_type = ConversationType.INSIGHT_GEN_CONVERSATION.value
            conversation_service.set_conversation_type(conversation_id, conversation_type)
            agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
            prompt = {
                "query": "",
                "initial_data": "",
                "feedback": {
                    "hypothesis_id": hypothesis_id,
                    "thought_chain_index": thought_index,
                    "comment": comment,
                },
            }

        return prompt, agent_type, is_user_feedback

    def _handle_session_type_logic(
        self,
        request_session_type: str,
        prompt: str,
        conversation_id: str,
        reaction_info: dict,
        dashboard_insight_info: dict,
        conversation_service: ConversationService,
    ) -> str:
        """Handle session type specific logic."""
        user_question = prompt
        request_session_type_enum = self._get_session_type_enum(request_session_type)

        if request_session_type_enum == SessionType.USER_REACTION:
            user_question = "Thanks for the feedback, we are updating the knowledge..."
            conversation_service.save_session_user_reaction(conversation_id, reaction_info)

            if reaction_info and reaction_info.get("reaction") == UserReaction.THUMBS_DOWN.value:
                raise ValueError("User reaction thumbs down is not supported yet")

        elif request_session_type_enum == SessionType.ADD_TO_DASHBOARD:
            user_question = "We are updating the insight dashboard..."
            conversation_service.save_add_to_dashboard_session(conversation_id, dashboard_insight_info)

        return user_question

    def _get_session_type_enum(self, request_session_type: str) -> SessionType:
        """Convert request session type string to SessionType enum."""
        request_session_type_enum = SessionType.ANALYSIS  # Default value
        if request_session_type:
            for session_type in SessionType:
                if session_type.value == request_session_type:
                    request_session_type_enum = session_type
                    break
        return request_session_type_enum

    def _setup_conversation_instance(
        self,
        conversation_instance: Conversation,
        session_instance: Session,
        conversation_db_obj: dict,
        history_messages: list,
        user_info: dict,
        request_session_type_enum: SessionType,
        dashboard_insight_info: dict,
        is_user_feedback: bool,
        global_memory: GlobalMemoryService,
    ):
        """Setup conversation instance with all necessary data."""
        global_memory.active_conversations[str(conversation_instance.chat_id)] = conversation_instance

        # Update conversation title flag for followed up insight question
        if conversation_db_obj.get("type") == ConversationType.INSIGHT_GEN_CONVERSATION.value:
            if not conversation_db_obj.get("name", "").startswith("@ Insight"):
                conversation_instance.is_conversation_title_required = True

        conversation_service = ConversationService()
        conversation_service.set_conversation_data(conversation_db_obj, conversation_instance)
        conversation_instance.active_session = session_instance
        conversation_instance.message_history = history_messages
        conversation_instance.active_session.user_info = user_info

        if request_session_type_enum == SessionType.ADD_TO_DASHBOARD:
            conversation_instance.active_session.dashboard_pin_request = dashboard_insight_info

        # Feedback is valid only for followup questions
        if len(history_messages) > 0:
            conversation_instance.is_user_feedback = is_user_feedback

    def _handle_file_upload_case(self, conversation_instance: Conversation):
        """Handle the case when it's a file upload with empty content."""
        msg = "Please feel free to ask any questions you might have regarding the content of your uploaded documents. I am here to assist you with any information or clarifications you need"
        conversation_instance.active_session.add_section()
        conversation_instance.add_to_history({"role": "assistant", "content": str(msg)}, False, True)
        conversation_instance.active_session.add_to_queue(msg, "assistant", ConversationStatus.IN_PROGRESS)
        conversation_instance.set_active_session_llm_status(ConversationLLMStatus.LLM_FINISHED)
        conversation_instance.is_conversation_title_required = False

    def _generate_question_suggestions(self, prompt: str, user_id: str):
        """Generate question suggestions for the user."""
        if not (
            prompt.lower().startswith("@insight")
            or prompt.lower().startswith("@suggestion")
            or prompt.lower().startswith("@feedback")
            or prompt.lower().startswith("@thought_feedback")
        ):
            user_service = UserService(user_id)
            self.thread_pool.submit(user_service.generate_suggested_questions, question=prompt)

    def create_conversation(
        self,
        user_info: dict,
        request_data: Dict[str, Any],
        conversation_service: ConversationService,
    ) -> Tuple[bool, Any, Optional[str]]:
        """
        Generic method to create conversations that can be used by both REST API and WebSocket services.

        This method encapsulates the core logic from the chat controller's create_conversation method,
        making it reusable across different transport layers.

        Args:
            user_info: User information dictionary
            request_data: Request data containing parent_conversation_id, etc.
            conversation_service: Conversation service instance

        Returns:
            Tuple of (success: bool, result: Any, error_message: Optional[str])
        """
        try:
            # Validate user info
            if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
                return False, None, ChatAppUserMessages.TEAM_NOT_EXIST.value

            user_id = user_info["id"]
            user_name = user_info.get("name", "")
            parent_conversation_id = request_data.get("parent_conversation_id")

            # Check if parent conversation id is provided. If so we create a child conversation and return
            if parent_conversation_id:
                # Load the parent conversation instance
                parent_conversation_instance = conversation_service.load_conversation(parent_conversation_id, user_id)
                if not parent_conversation_instance:
                    return False, None, ChatAppUserMessages.CHAT_CAN_NOT_FIND.value

                child_conversation, session_id = conversation_service.create_conversation_and_session(
                    user_id,
                    parent_conversation_instance.user_inputs[0],
                    LLMAgentType.AGENT_TYPE_CHAT,
                    parent_conversation=parent_conversation_instance,
                    is_hide_from_end_user=True,
                    is_copy_agent_states=True,
                )

                # Save the child conversation in DB so it will have the parent's history
                child_conversation.save(logger)
                # change conversation status again to queued to mark it as fresh
                conversation_service.update_conversation_status(
                    child_conversation.chat_id, ConversationStatus.QUEUED.value
                )

                return (
                    True,
                    {"isSuccess": True, "conversationId": child_conversation.chat_id, "sessionId": session_id},
                    None,
                )

            # Create regular conversation
            res = conversation_service.add_new_conversation(
                user_id, user_name, ConversationType.CHAT_BOT_CONVERSATION.value
            )

            if res.get("isSuccess"):
                return True, res, None
            else:
                return False, None, res.get("message", "Failed to create conversation")

        except Exception as e:
            return False, None, str(e)

    def run(
        self,
        conversation: Conversation,
        log_path: str,
        user_info: dict,
    ):
        """
        Execute the orchestration agent to handle user prompt.

        Args:
            conversation (Conversation): The conversation object
            log_path: Path to the log file

        Returns:
            Agent response
        """
        current_date_string = datetime.now().strftime("%Y-%m-%d")

        log_file_full_path = f"./logs/{log_path}chat_{conversation.chat_id}.log"
        if not os.path.exists(pathlib.Path(log_file_full_path).parent):
            os.makedirs(pathlib.Path(log_file_full_path).parent)

        conversation.log_file_full_path = log_file_full_path
        chat_log = get_debug_logger(f"chat_{conversation.chat_id}", log_file_full_path)
        user_prompt = conversation.user_inputs[-1]

        initial_frontend_tab = [
            {
                "id": FrontendTabContentType.ANSWER.tab_type_value,
                "title": FrontendTabContentType.ANSWER.tab_title_value,
                "status": conversation.active_session.session_status.value,
                "session_type": conversation.active_session.session_type.value,
            }
        ]

        # If this is insight query, then mark it (using @Insight prompt)
        is_insight = False
        if conversation.active_session.session_type == SessionType.DEEP_RESEARCH:
            user_prompt = user_prompt.replace("@insight", "", 1)
            is_insight = True
            conversation.agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
            initial_frontend_tab = [
                {
                    "id": FrontendTabContentType.DEEP_RESEARCH.tab_type_value,
                    "title": FrontendTabContentType.DEEP_RESEARCH.tab_title_value,
                    "status": conversation.active_session.session_status.value,
                    "session_type": conversation.active_session.session_type.value,
                }
            ]
            conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.DEEP_RESEARCH)
        elif conversation.active_session.session_type == SessionType.COMPLEX_ANALYSIS:
            user_prompt = user_prompt.replace("@complex", "", 1)
            conversation.agent_type = LLMAgentType.AGENT_TYPE_COMPLEX_ANALYSIS
            conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.ANSWER)
        elif conversation.active_session.session_type == SessionType.USER_REACTION:
            conversation.agent_type = LLMAgentType.AGENT_TYPE_USER_REACTION
            # user_prompt = "Thanks for the feedback, we are updating the knowledge..."
        elif conversation.active_session.session_type == SessionType.ADD_TO_DASHBOARD:
            conversation.agent_type = LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT
            # user_prompt = "We are updating the insight dashboard..."
        else:
            conversation.active_session.session_type = SessionType.ANALYSIS
            conversation.agent_type = LLMAgentType.AGENT_TYPE_CHAT
            conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.ANSWER)
        chat_log.info(f"OrchestrationAgent | Processing prompt: {user_prompt}")

        # send user question as a block to frontend
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.USER_QUESTION.suffix_value}",
            block_data=user_prompt,
            block_type=FrontendBlockType.USER_QUESTION,
            block_tab_types=[FrontendTabContentType.QUERY],
        )

        # add initial tab to frontend
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.TAB_CONTENT.suffix_value}",
            block_type=FrontendBlockType.TAB_CONTENT,
            block_data=initial_frontend_tab,
            block_tab_types=[FrontendTabContentType.TABS],
        )

        # need to send empty data source block in order to keep frontend block order (data sources block should be before markdown block)
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.DATA_SOURCES.suffix_value}",
            block_type=FrontendBlockType.DATA_SOURCES,
            block_data=[],
            block_tab_types=[FrontendTabContentType.SOURCES, FrontendTabContentType.ANSWER],
        )

        # add task tab to db to show in frontend when retrieving from history
        conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.TASKS)
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.TAB_CONTENT.suffix_value}",
            block_type=FrontendBlockType.TAB_CONTENT,
            block_data=[
                {
                    "id": FrontendTabContentType.TASKS.tab_type_value,
                    "title": FrontendTabContentType.TASKS.tab_title_value,
                    "status": conversation.active_session.session_status.value,
                    "session_type": conversation.active_session.session_type.value,
                }
            ],
            handler_type=DataBlockHandlerType.PERSIST,
            block_tab_types=[FrontendTabContentType.TABS],
        )

        # Pre-Steps:
        if conversation.file_upload_sessions:
            # Process file uploads
            is_files_handled = self.handle_user_uploaded_files(conversation)
            if not is_files_handled.success:
                return is_files_handled.message, conversation

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.PRE_PROCESSING_USER_PROMPT.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )

        try:
            # Step 1: Handle potential follow-up questions
            # In case of complex query agent, combined query is not used - use original user prompt in followup
            if conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_ANALYSIS:
                enriched_prompt = user_prompt
                # check conversation history to check if this followup
                # conversation_history = conversation.get_llm_input_history([SectionType.QUESTION.value])
                if AgentState.COMPLEX_REASONING in conversation.agent_state_follow_up_history:
                    last_history_count = max(
                        conversation.agent_state_follow_up_history[AgentState.COMPLEX_REASONING].keys()
                    )
                    conversation.is_continue_previous = last_history_count > 0
            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT:
                enriched_prompt = user_prompt
                # check conversation history to check if this followup
                if AgentState.COMPLEX_REASONING in conversation.agent_state_follow_up_history:
                    last_history_count = max(
                        conversation.agent_state_follow_up_history[AgentState.COMPLEX_REASONING].keys()
                    )
                    conversation.is_continue_previous = last_history_count > 0
            else:
                enriched_prompt = self.followup_handler.execute_agent(
                    user_prompt, conversation, conversation.active_session.session_id, chat_log
                )

            # Save the current user question to conversation history - excluding @approve
            # if not user_prompt.startswith("@approve"):
            if (
                conversation.agent_type != LLMAgentType.AGENT_TYPE_USER_REACTION
                or conversation.agent_type != LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT
            ):
                conversation.add_to_history(
                    {"role": "user", "content": str(user_prompt)},
                    True,
                    True,
                    is_required_for_follow_ups=True,
                    llm_section_type=SectionType.QUESTION.value,
                )
                conversation.user_inputs[-1] = enriched_prompt

            if is_insight:
                enriched_prompt = "@Insight " + enriched_prompt

            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_USER_REACTION:
                conversation.agent_state = AgentState.KNOWLEDGE_MANAGER
                knowledge_manager_res = self.start(conversation, None, {"user_request": enriched_prompt}, log_path)
                # Final answer
                result, conversation_temp = self.answer_prepare.run(
                    enriched_prompt, conversation, conversation.active_session.session_id, log_path
                )
                return result, conversation

            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_ANALYSIS:
                conversation.agent_state = AgentState.COMPLEX_REASONING
                complex_analysis_res = self.start(
                    conversation,
                    None,
                    {"user_request": enriched_prompt, "current_date": current_date_string},
                    log_path,
                )
                # Final answer
                result, conversation_temp = self.answer_prepare.run(
                    enriched_prompt, conversation, conversation.active_session.session_id, log_path
                )

                # send email if process time more than email time threshold
                print(f"this is the start time: {conversation.active_session.session_start_time}")
                print(f"this is the current time: {datetime.now(timezone.utc)}")
                print(
                    f"this is the start time + threshold: {conversation.active_session.session_start_time + timedelta(seconds=self.email_time_threshold)}"
                )
                if conversation.active_session.session_start_time + timedelta(
                    seconds=self.email_time_threshold
                ) < datetime.now(timezone.utc):
                    self.conversation_service.send_last_session_answer_email(
                        conversation.chat_id, chat_log, [user_info.get("email")], user_info
                    )

                return result, conversation

            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT:
                # invoke InsightDashboardService to generate insight
                insight_dashboard_service = InsightDashboardService()
                insight_dashboard_service.add_analysis_to_dashboard(
                    conversation,
                    conversation.active_session.dashboard_pin_request.sessionId,
                    conversation.active_session.dashboard_pin_request.frequencyInfo,
                    conversation.active_session.user_info,
                )
                result, conversation_temp = self.answer_prepare.run(
                    enriched_prompt, conversation, conversation.active_session.session_id, log_path
                )
                return result, conversation

            # Step 2: Determine the appropriate tool of the query
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.INTENT_CLASSIFICATION.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            )
            tool_result = self.tool_router.execute_agent(
                {"user_question": enriched_prompt}, conversation, conversation.active_session.session_id, chat_log
            )
            if tool_result is None:
                chat_log.error("OrchestrationAgent | Failed to classify intent")
                return "I encountered an error while processing your request.", conversation

            if is_insight:
                self.insight_agent = LlmInsightAgent(True)
                insight_user_prompt = {
                    "query": enriched_prompt,
                    "initial_data": "",
                    "feedback": "",
                }
                return self.insight_agent.run(
                    insight_user_prompt, conversation, conversation.active_session.session_id, log_path
                )

            result_dict = {
                "is_success": False,
                "error_message": "",
            }
            # Step 3: Route to appropriate agent based on intent
            if tool_result.get("data_sources"):
                data_source_name = tool_result.get("data_sources")[0]  # Assume only one data source for now

                if tool_result.get("route_option") == "DATA":
                    # Handle SQL-related queries by SQL agent with selected data source
                    self.sql_agent = SQLAgent(data_source_name)
                    chat_log.info(
                        f"OrchestrationAgent | Routing to SQL Agent to retrieve data from {data_source_name}"
                    )
                    result_dict = self.sql_agent.start(conversation, data_source_name, None, log_path)
                    # Invoke the python code generation agent if data retrieval is successful and valid data is returned
                    if result_dict.get("total_record_count", 0) > 0 and (
                        conversation.data_retrieval_status == ConversationConclusion.DATA_AVAILABLE
                        or conversation.data_retrieval_status == ConversationConclusion.TRUNCATED_RECORDS
                    ):
                        chat_log.info(
                            "OrchestrationAgent | Data retrieval successful. Invoking Python code generation agent"
                        )
                        conversation.agent_state = AgentState.PYTHON_CODE_GENERATION
                        result_dict["user_question"] = conversation.user_inputs[-1]
                        self.start(conversation, data_source_name, result_dict, log_path)
                    else:
                        chat_log.info("OrchestrationAgent | Data retrieval aborted.")
                        conversation.agent_state = AgentState.FAILED
                elif tool_result.get("route_option") == "METADATA":
                    # Handle metadata queries
                    input_data = {
                        "user_question": conversation.user_inputs[-1],
                    }
                    conversation.agent_state = AgentState.METADATA_QUERY
                    self.start(conversation, data_source_name, input_data, log_path)
                else:
                    # Unsupported option
                    pass

            elif tool_result.get("route_option") == "PYTHON":
                # Handle calculations
                input_data = {
                    "user_question": conversation.user_inputs[-1],
                }
                conversation.agent_state = AgentState.PYTHON_CODE_GENERATION
                conversation.data_retrieval_status == ConversationConclusion.DATA_AVAILABLE
                self.start(conversation, None, input_data, log_path)

            # If valid answer is there, then save it for the use of query router
            if conversation.data_retrieval_status == ConversationConclusion.DATA_AVAILABLE:
                self.tool_router.add_to_history(
                    conversation,
                    "user",
                    ["Previous Question: " + enriched_prompt],
                    is_required_for_follow_ups=True,
                )
                if conversation.current_question_raw_answer:
                    self.tool_router.add_to_history(
                        conversation,
                        "assistant",
                        ["Previous Answer: " + conversation.current_question_raw_answer],
                        is_required_for_follow_ups=True,
                    )

            # Step 4: Prepare final answer
            result, conversation_temp = self.answer_prepare.run(
                enriched_prompt, conversation, conversation.active_session.session_id, log_path
            )

            return result, conversation

        except Exception as e:
            error_msg = f"OrchestrationAgent | Error: {str(e)}\n{traceback.format_exc()}"
            chat_log.error(error_msg)
            conversation.status = ConversationStatus.FAILED
            return f"I encountered an error while processing your request: {str(e)}", conversation

    def handle_python_code_generation_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):

        # After Python code generation, move to code review
        next_input = {
            "user_question": conversation.user_inputs[-1],
            "python_code": output_dict["code"],
            "reasoning": output_dict["logic"],
            "reusable_variables": conversation.global_var_list,
            "file_paths": output_dict["file_paths"],
        }

        return AgentState.CODE_REVIEW, next_input

    def handle_code_review_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the code review agent.

        Args:
            output_dict (dict): The output from the code review agent
            prev_invoked_count (int): Number of previous invocations
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging

        Returns:
            tuple: Next state and input data for the next state
        """
        # Check if revision is required
        if output_dict.get("is_revision_required", False):
            chat_log.info("SQLAgent | handle_code_review_output | Code review failed. Revising Python code")
            # If revision is required, go back to Python code generation
            next_input = {
                "review_feedback": output_dict.get("review_feedback", ""),
            }
            return AgentState.PYTHON_CODE_GENERATION, next_input

        # Reset the history for code review when it passes
        self.state_handler_instance_map[AgentState.CODE_REVIEW].clear_history(conversation)

        # If no revision is required, move to Python execution
        python_code = output_dict.get("python_code", "")

        next_input = {
            "user_question": conversation.user_inputs[-1],
            "python_code": python_code,
            "file_paths": output_dict.get("file_paths", {}),
        }

        # Move to Python execution
        return AgentState.PYTHON_CODE_EXECUTION, next_input

    def handle_python_execution_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the Python execution agent.

        Args:
            output_dict (dict): The output from the Python execution agent
            prev_invoked_count (int): Number of previous invocations
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging

        Returns:
            tuple: Next state and input data for the next state
        """
        # Check if execution was successful
        if not output_dict.get("execution_success", False):
            chat_log.info("SQLAgent | handle_python_execution_output | Execution failed. Revising Python code")
            # If execution failed, go back to Python code generation
            next_input = {
                "execution_error": output_dict.get("execution_error", "Unknown execution error"),
            }
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.PYTHON_CODE_GENERATION, next_input

        # Add the execution result to the history of python code generator agent
        self.state_handler_instance_map[AgentState.PYTHON_CODE_GENERATION].add_to_history(
            conversation,
            "user",
            [f"Execution Result: {output_dict.get('execution_result', '')}"],
            is_required_for_follow_ups=True,
        )

        # Move to data review
        conversation.current_question_raw_answer = str(output_dict.get("execution_result", ""))
        return AgentState.TEXTUAL_REVIEW, output_dict

    def handle_data_review_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the data review agent.

        Args:
            output_dict (dict): The output from the data review agent
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging

        Returns:
            tuple: Next state and input data for the next state
        """
        # Check if revision is required
        if output_dict.get("is_revision_required", False):
            chat_log.info("SQLAgent | handle_data_review_output | Data review failed. Revising Python code")
            # If revision is required, go back to Python code generation
            next_input = {
                "review_feedback": output_dict.get("feedback", ""),
                "execution_result": output_dict.get("data_observations", ""),
            }
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.PYTHON_CODE_GENERATION, next_input

        # If there are image files to review, move to visual review
        if output_dict.get("image_file_paths", []):
            # clear the history of python code writing and execution agents because now data issues are no more
            self.state_handler_instance_map[AgentState.PYTHON_CODE_GENERATION].clear_history(conversation, True)
            self.state_handler_instance_map[AgentState.PYTHON_CODE_EXECUTION].clear_history(conversation)
            chat_log.info("SQLAgent | handle_data_review_output | Moving to visual review")
            next_input = {
                "user_question": conversation.user_inputs[-1],
                "python_code": output_dict.get("python_code", ""),
                "execution_result": output_dict.get("execution_result", ""),
                "image_file_paths": output_dict.get("image_file_paths", []),
            }
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            return AgentState.VISUAL_REVIEW, next_input

        # If no revision is required and no images to review, mark as completed
        conversation.agent_state = AgentState.COMPLETED
        conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
        return AgentState.COMPLETED, {}

    def handle_visual_review_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the visual review agent.

        Args:
            output_dict (dict): The output from the visual review agent
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging
            is_last_attempt (bool): Whether this is the last attempt

        Returns:
            tuple: Next state and input data for the next state
        """
        # TODO: Mark answer correct even if visualization is not correct - required for insight
        # Check if revision is required
        if output_dict.get("is_revision_required", False):
            chat_log.info("SQLAgent | handle_visual_review_output | Visual review failed. Revising Python code")
            # If revision is required, go back to Python code generation
            next_input = {
                "review_feedback": output_dict.get("feedback", ""),
                "is_visualization_issue": output_dict.get("is_visualization_issue", False),
            }

            # Store feedback in conversation history if it's a visualization issue
            if output_dict.get("is_visualization_issue", False):
                if not hasattr(conversation, "data_reviewer_feedback_history"):
                    conversation.data_reviewer_feedback_history = []

                conversation.data_reviewer_feedback_history.append(
                    {
                        "feedback": output_dict.get("feedback", ""),
                        "is_visualization_issue": True,
                        "issue_severity": output_dict.get("issue_severity", ""),
                    }
                )

            return AgentState.PYTHON_CODE_GENERATION, next_input

        # If no revision is required, mark as completed
        conversation.agent_state = AgentState.COMPLETED
        return AgentState.COMPLETED, {}

    def handle_metadata_query_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the metadata query agent.

        Args:
            output_dict (dict): The output from the metadata query agent
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging
            is_last_attempt (bool): Whether this is the last attempt

        Returns:
            tuple: Next state and input data for the next state
        """
        # If metadata query was successful, mark as completed
        if output_dict.get("is_success", False):
            conversation.agent_state = AgentState.COMPLETED
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            conversation.current_question_raw_answer = output_dict.get("answer", "")
            return AgentState.COMPLETED, {}
        else:
            # If metadata query failed, mark as failed
            conversation.agent_state = AgentState.FAILED
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.FAILED, {}

    def handle_user_uploaded_files(self, conversation: Conversation) -> GeneralResponse[bool]:
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )
        # wait until all files are uploaded
        is_upload_sessions_ready = self.upload_session_service.wait_until_all_files_uploaded(
            conversation.file_upload_sessions
        )
        if not is_upload_sessions_ready.success:
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS_FAILED.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            )
            # return is_upload_sessions_ready.message, conversation
            return GeneralResponse(
                success=False,
                message=is_upload_sessions_ready.message,
                data=False,
            )

        else:
            # copy uploaded sessions' files to respective conversation directory
            is_files_copied = self.upload_session_service.copy_uploaded_files_to_conversation_directory(
                str(conversation.chat_id), conversation.file_upload_sessions, conversation
            )
            if is_files_copied.success:
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS_SUCCESS.value,
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                )
                if is_files_copied.data:
                    # send data sources tab to frontend
                    if FrontendTabContentType.SOURCES not in conversation.active_session.session_used_tabs_all:
                        conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.SOURCES)
                        conversation.persist_and_stream_handler(
                            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.TAB_CONTENT.suffix_value}",
                            block_type=FrontendBlockType.TAB_CONTENT,
                            block_data=[
                                {
                                    "id": FrontendTabContentType.SOURCES.tab_type_value,
                                    "title": FrontendTabContentType.SOURCES.tab_title_value,
                                    "status": conversation.active_session.session_status.value,
                                    "session_type": conversation.active_session.session_type.value,
                                }
                            ],
                            block_tab_types=[FrontendTabContentType.TABS],
                        )
                    # stream all files as data sources
                    conversation.persist_and_stream_handler(
                        block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.DATA_SOURCES.suffix_value}",
                        block_type=FrontendBlockType.DATA_SOURCES,
                        block_data=is_files_copied.data,
                        block_tab_types=[FrontendTabContentType.SOURCES, FrontendTabContentType.ANSWER],
                    )
            else:
                # stream error message
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS_FAILED.value,
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                )
                # return is_files_copied.message, conversation
                return GeneralResponse(
                    success=False,
                    message=is_files_copied.message,
                    data=False,
                )
        return GeneralResponse(
            success=True,
            message="Files processed successfully",
            data=True,
        )

    def handle_complex_reasoning_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        # If complex reasoning was successful, mark as completed
        if output_dict.get("is_success", False):
            conversation.agent_state = AgentState.COMPLETED
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            return AgentState.COMPLETED, {}
        else:
            # If complex reasoning failed, mark as failed
            conversation.agent_state = AgentState.FAILED
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.FAILED, {}

    def handle_knowledge_manager_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        # If knowledge manager was successful, mark as completed
        if output_dict.get("status", "") == "completed":
            conversation.agent_state = AgentState.COMPLETED
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            return AgentState.COMPLETED, {}
        else:
            # If knowledge manager failed, mark as failed
            conversation.agent_state = AgentState.FAILED
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.FAILED, {}
