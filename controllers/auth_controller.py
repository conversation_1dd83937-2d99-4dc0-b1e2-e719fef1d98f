"""
* Copyright (c) 2024 ZOOMi Technologies Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* AuthController use for handling authentication-related operations.

* @class AuthController
* @description This controller use for Handle the request related to the authentication. Ex: refresh token
* <AUTHOR>
"""

import os
import pathlib
from pydantic import BaseModel
from typing import Optional, Dict, Any
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from fastapi_utils.cbv import cbv
from fastapi_utils.inferring_router import InferringRouter
from services.custom_token_service import refresh_token
from services.sso_user_service import SsoUserService
import requests

from utils.logger import get_debug_logger

if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))
logger = get_debug_logger(
    "Auth Controller",
    pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log"),
)

router = InferringRouter()


class RefreshGrant(BaseModel):
    refreshToken: str


class Credentials(BaseModel):
    """
    Model representing user login credentials.
    """

    email: str
    password: str


class LoginResult(BaseModel):
    """
    Model representing login response.
    """

    token: str
    user: Optional[Dict[str, Any]] = None


@cbv(router)
class AuthController:
    def __init__(self):
        self.sso_user_service = SsoUserService()

    @router.post("/api/refresh-token")
    async def refresh_token(self, request: RefreshGrant):
        """
        Description: Refreshes an access token using a provided refresh token.
        Parameters:
            - request: RefreshGrant, a request model containing the refresh token.
        Returns:
            - dict: Dictionary containing the new access token and refresh token.
        Raises:
            - HTTPException: If there is an issue with the provided refresh token, such as expiration or invalidity.
                - status_code: HTTP status code indicating the issue.
                - detail: Error message providing details about the issue.
                - headers: HTTP headers indicating the authentication method.
        """
        token_object = await refresh_token(request.refreshToken)
        return {
            "token": token_object["accessToken"],
            "refreshToken": token_object["refreshToken"],
        }

    @router.post("/api/login", response_model=LoginResult)
    async def login(self, credentials: Credentials) -> LoginResult:
        """
        Expose Login end point to Annotation tool frontend
        Authenticate user and return the JWT token / refresh token pair to use in subsequent requests

        Parameters:
            - credentials: Object including email and password (format: { "email": "email_address", "password": "password" })

        Returns:
            - LoginResult: Token and user object if authentication successful

        Raises:
            - HTTPException: 401 if authentication fails, 500 for server errors
        """
        try:
            logger.info(f"Login attempt for user: {credentials.email}")

            # Convert credentials to dict for SSO service
            credentials_dict = {"email": credentials.email, "password": credentials.password}

            # Route request to SSO endpoint through SsoUserService
            login_response = self.sso_user_service.login(credentials_dict)

            # Return the response in the expected format
            return LoginResult(token=login_response.get("token", ""), user=login_response.get("user"))

        except requests.exceptions.HTTPError as http_err:
            logger.error(f"Login failed for user {credentials.email}: HTTP {http_err.response.status_code}")
            if http_err.response.status_code == 401:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
            else:
                raise HTTPException(status_code=http_err.response.status_code, detail="Authentication failed")
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Login failed for user {credentials.email}: {str(req_err)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Authentication service unavailable"
            )
        except Exception as e:
            logger.error(f"Unexpected error during login for user {credentials.email}: {str(e)}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
