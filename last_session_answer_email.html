
	<!DOCTYPE html>
	<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<style>
			@media only screen and (max-width:600px) {
				.maintab {
					border-radius: 0 0 16px 16px;
				}
			}
			.markdown-content table {
				border-collapse: collapse;
				margin: 20px 0;
			}
			.markdown-content table th,
			.markdown-content table td {
				border: 1px solid #303642;
				padding: 10px 12px;
				text-align: left !important;
				color: #FFFFFF;
                font-size: 12px;
			}
			.markdown-content table th {
                border: 1px solid #303642;
				background-color: #131924;
				font-weight: 600;
                font-size: 12px;
			}
			.markdown-content ul, .markdown-content ol {
				padding-left: 20px;
				color: #E3E3E3;
				margin: 16px 0;
			}
			.markdown-content li {
				margin: 8px 0;
				color: #E3E3E3;
				line-height: 1.5;
			}
			.markdown-content p {
				margin: 16px 0;
				color: #E3E3E3;
				line-height: 1.6;
			}
			.markdown-content h1, .markdown-content h2, .markdown-content h3 {
				color: #C8C8C8;
				margin: 20px 0 16px 0;
			}
			.markdown-content code {
				background-color: #374151;
				padding: 2px 4px;
				border-radius: 3px;
				color: #E3E3E3;
			}
			.markdown-content pre {
				background-color: #374151;
				padding: 16px;
				border-radius: 8px;
				overflow-x: auto;
				color: #E3E3E3;
			}
		</style>
	</head>
	<body class="body" style="font-family: 'Inter', sans-serif; background-color: #131924; background-image: linear-gradient(180deg, #131924, #131924); margin: 0; padding: 0; text-align: left;">
		<div style="width: 100%; max-width: none; font-family: 'Inter', sans-serif; background-color: #131924; background-image: linear-gradient(180deg, #131924, #131924); color: #fff; margin: 0; padding: 0; text-align: left;">
			<table class="maintab" cellpadding="0" cellspacing="0" style="background-color: #18202E; background-image: linear-gradient(180deg, #18202E, #18202E); max-width: 700px; margin: auto; margin-bottom: 50px; border-radius: 16px;">
				<tr>
					<td style="padding: 30px;">
						<img src="https://chat.None/api/file/public/icons?file_name=layernext_logo_black_bg.png" alt="Company Logo" style="width: 133.303px; height: 20px; margin: 20px 0;" />
						<div style="font-size: 30px; font-weight: 700; line-height: 41px; letter-spacing: -0.6px; margin: 8px 0; color: #fff;">
							<div style="background: #000; mix-blend-mode: screen;">
								<div style="background: #000; mix-blend-mode: difference;">
									Calculate Last Month Revenue
								</div>
							</div>
						</div>
						<div style="font-size: 14px; color: #5D626C; font-weight: 400; padding: 5px 0; margin-bottom: 15px; line-height: 18px;">
							<div style="background: #000; mix-blend-mode: screen;">
								<div style="background: #000; mix-blend-mode: difference;">
									Generated on August 20, 2025
								</div>
							</div>
						</div>
					</td>
				</tr>
				<tr><td><hr style="border-color:#bbbbbb57; margin: 0; padding: 0;"></td></tr>
				
				
            <tr>
                <td style="padding: 15px 30px 0 30px;">
                    <div class="markdown-content" style="font-size: 16px; line-height: 1.6; color: #E3E3E3;">
                        <p><strong>Revenue – Last Month (July 2025)</strong></p>
<p>The company generated <strong>$304,475</strong> in revenue during July 2025.</p>
                    </div>
                </td>
            </tr>
        
            <tr>
                <td style="padding: 15px 30px 0 30px;">
                    <div class="markdown-content" style="font-size: 16px; line-height: 1.6; color: #E3E3E3;">
                        <table>
<thead>
<tr>
<th style="text-align: left;">month_start</th>
<th style="text-align: right;">total_revenue</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;">{'value': '2025-07-01'}</td>
<td style="text-align: right;">304475</td>
</tr>
</tbody>
</table>
                    </div>
                </td>
            </tr>
        
            <tr>
                <td style="padding: 15px 30px 0 30px;">
                    <div class="markdown-content" style="font-size: 16px; line-height: 1.6; color: #E3E3E3;">
                        <p>You can download the underlying data below:</p>
                    </div>
                </td>
            </tr>
        
            <tr>
                <td style="padding: 15px 30px 0 30px;">
                    
                    <div style="margin: 10px 0; border-radius: 8px;">
                        <a href="http://localhost:5082/api/file/public/68a5276df76bc1a1abc3bd3a/download?file_name=july_2025_total_revenue.csv" style="font-weight: 600; color: #E3E3E3;">
                            july_2025_total_revenue.csv
                        </a>
                    </div>
                
                </td>
            </tr>
        
				
				<tr>
					<td style="display: block; padding: 30px; margin: auto; text-align: center;">
						<a href="https://chat.None/history/68a5276df76bc1a1abc3bd3a" style="text-decoration: none;">
							<img src="https://chat.None/api/file/public/icons?file_name=check_it_out_button_v2.png" style="margin-bottom: 50px !important;" />
						</a>
					</td>
				</tr>
			</table>
			<table cellpadding="0" cellspacing="0" style="max-width: 700px; margin: auto; padding-bottom: 50px;">
				<tr style="color: #5D626C; text-align: center; font-size: 12px; font-weight: 400; margin-bottom: 50px;">
					<td style="padding: 30px;">
						<img src="https://chat.None/api/file/public/icons?file_name=layernext_logo_black_bg.png" alt="Company Logo" style="width: 133.303px; height: 20px;" />
						<p>Generated by LayerNext on August 20, 2025.</p>
						<p>&copy; 2025 LayerNext, Inc. | <a href="https://www.layernext.ai/privacy-policy" style="color: #BBB; text-decoration: underline;">Privacy Policy</a></p>
						<a href="#" style="color: #BBB; text-decoration: underline;">Unsubscribe</a>
					</td>
				</tr>
			</table>
		</div>
	</body>
	</html>
    