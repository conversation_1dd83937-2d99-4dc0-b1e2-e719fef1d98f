# Use layernext-python-sdk to test quickbooks_get_auth_uri method
from layernext import LayerNextClient

# Load data lake API credentials from .env
import os
from dotenv import load_dotenv

load_dotenv()

api_key = os.getenv("API_KEY")
secret = os.getenv("SECRET_KEY")
layernext_url = os.getenv("URL")

# Create a LayerNextClient instance
client = LayerNextClient(api_key, secret, layernext_url)

# Test quickbooks_get_auth_uri method
auth_uri = client.quickbooks_get_auth_uri()
print(auth_uri)
