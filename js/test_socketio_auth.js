// Socket.IO client test with authentication
// This demonstrates the complete flow: create conversation → send query → connect to conversation

const { io } = require('socket.io-client');

// Configuration
const SOCKET_URL = 'http://localhost:5082'; // Update this to your server URL
//const SOCKET_URL = 'https://chat.dev.layernext.ai'; // Update this to your server URL
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************.hbQqFXwemFF8WdNq55z0og74iAcxnphMCiS4vMwmC5U'; // Replace with actual token
const QUESTION = "What is revenue last month?";
const QUESTION_FOLLOWUP = "Plot monthly revenue in 2025";
//const QUESTION = "How many appointments in 2025?";
// Global variables to track conversation flow
let createdConversationId = null;
let createdSessionId = null;
let isFirstAnswerReceived = false;
// Connection options with authentication
const connectionOptions = {
    // Method 1: Token in query parameters
    query: {
        token: AUTH_TOKEN
    },
    
    // Method 2: Token in headers (alternative)
    extraHeaders: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        // Or use custom header:
        // 'X-Auth-Token': AUTH_TOKEN
    },
    
    // Socket.IO options
    transports: ['polling'],
    timeout: 2000000,
    pingTimeout: 6000000,
    forceNew: false
};

console.log('🔌 Socket.IO Complete Flow Test Client');
console.log('=====================================');
console.log(`Server URL: ${SOCKET_URL}`);
console.log(`Auth Token: ${AUTH_TOKEN.substring(0, 20)}...`);
console.log('');

// Create Socket.IO client
const socket = io(SOCKET_URL, connectionOptions);

// Connection event handlers
socket.on('connect', () => {
    console.log('✅ Connected to Socket.IO server');
    console.log(`🆔 Session ID: ${socket.id}`);
    console.log('');
});

socket.on('disconnect', (reason) => {
    console.log('❌ Disconnected from Socket.IO server');
    console.log(`📝 Reason: ${reason}`);
    console.log('');
    process.exit(0);
});

socket.on('connect_error', (error) => {
    console.log('❌ Connection error:');
    console.log(`   Error: ${error.message}`);
    console.log(`   Type: ${error.type}`);
    console.log('');
    process.exit(1);
});

// Authentication event handlers
socket.on('auth_success', (data) => {
    console.log('🔐 Authentication successful!');
    console.log(`   Message: ${data.message}`);
    console.log(`   User: ${data.user.email}`);
    console.log(`   User ID: ${data.user.id}`);
    console.log(`   User Type: ${data.user.userType}`);
    console.log(`   Timestamp: ${new Date(data.timestamp * 1000).toLocaleString()}`);
    console.log('');
    
    // Start the conversation flow after successful authentication
    setTimeout(() => {
        console.log('🚀 Starting conversation flow...');
        createConversation();
    }, 2000);
});

socket.on('auth_failed', (data) => {
    console.log('❌ Authentication failed!');
    console.log(`   Message: ${data.message}`);
    console.log(`   Timestamp: ${new Date(data.timestamp * 1000).toLocaleString()}`);
    console.log('');
    process.exit(1);
});

// Create conversation function
function createConversation() {
    console.log('📝 Creating new conversation...');
    
    const createConversationMessage = {
        type: 'create_conversation',
        parent_conversation_id: ''  // Empty for new conversation
    };
    
    socket.emit('message', createConversationMessage);
    console.log('📤 Sent create conversation request');
    console.log('');
}

// Create conversation success handler
socket.on('create_conversation_success', (data) => {
    console.log('✅ Conversation created successfully!');
    console.log(`   Data: ${JSON.stringify(data.data, null, 2)}`);
    
    // Extract conversation ID and session ID
    if (data.data && data.data.conversation_id) {
        createdConversationId = data.data.conversation_id;
        
        console.log(`   Conversation ID: ${createdConversationId}`);
        console.log('');
        
        // Wait 2 seconds then send the query
        setTimeout(() => {
            console.log('📤 Sending query to the created conversation...');
            sendQuery();
        }, 2000);
    } else {
        console.log('❌ Failed to extract conversation ID from response');
        console.log('');
    }
});

// Create conversation error handler
socket.on('create_conversation_error', (data) => {
    console.log('❌ Failed to create conversation!');
    console.log(`   Error: ${data.error}`);
    console.log(`   Timestamp: ${new Date(data.timestamp * 1000).toLocaleString()}`);
    console.log('');
});

// Send query function
function sendQuery() {
    if (!createdConversationId) {
        console.log('❌ No conversation ID available. Cannot send query.');
        return;
    }
    
    console.log('📤 Sending query...');
    
    const queryMessage = {
        type: 'send_query',
        role: 'user',
        content: isFirstAnswerReceived ? QUESTION_FOLLOWUP : QUESTION,
        contentType: 'text',
        id: createdConversationId,
        isFileUpload: false,
        sessionId: createdSessionId || '',
        insight_board_id: '',
        uploads: [],
        reconnect: false,
        session_type: 'complex_analysis'
    };
    
    socket.emit('message', queryMessage);
    console.log(`📤 Sent query to conversation: ${createdConversationId}`);
    console.log('');
    
    // Wait 3 seconds then connect to the conversation for updates
    setTimeout(() => {
        console.log('📡 Connecting to conversation updates...');
        connectToConversation();
    }, 3000);
}

// Connect to conversation function
function connectToConversation() {
    if (!createdConversationId) {
        console.log('❌ No conversation ID available. Cannot connect to conversation.');
        return;
    }
    
    console.log(`📡 Connecting to conversation: ${createdConversationId}`);
    
    socket.emit('connect_conversation', {
        conversation_id: createdConversationId,
    });
    
    console.log('📡 Connected to conversation updates');
    console.log('');
}

// Conversation event handlers
socket.on('query', (data) => {
    console.log('📡 Conversation query received!');
    console.log(`   Message: ${data.data.data}`);
    console.log(`   Status: ${data.status}`);
    console.log(`   Session ID: ${data.session_id}`);
    console.log(`   Conversation ID: ${data.conversation_id}`);
    console.log(`   Timestamp: ${data.timestamp}`);
    console.log('');
});

// Message event handlers
socket.on('message', (data) => {
    console.log('📥 Received message:');
    console.log(`   Data: ${JSON.stringify(data, null, 2)}`);
    console.log('');
});

// Error event handlers
socket.on('error', (data) => {
    console.log('❌ Server error:');
    console.log(`   Message: ${data.message}`);
    console.log(`   Timestamp: ${new Date(data.timestamp * 1000).toLocaleString()}`);
    console.log('');
    process.exit(1);
});

// Analysis progress event handlers
socket.on('progress_bar', (data) => {
    console.log('🔄 Analysis Progress Update:');
    console.log(`   Conversation: ${data.conversation_id}`);
    console.log(`   Session: ${data.session_id}`);
    console.log(`   Status: ${data.status}`);
    console.log(`   Session Type: ${data.session_type}`);
    
    if (data.data && data.data.data) {
        const progressData = data.data.data;
        console.log(`   Title: ${progressData.title}`);
        console.log(`   Progress: ${progressData.progress}%`);
        console.log(`   Current Step: ${progressData.currentStep}`);
        console.log(`   Streaming: ${progressData.isStreaming}`);
    }
    console.log('');
});

// Answer event handlers
socket.on('answer', (data) => {
    console.log('✅ Answer received:');
    console.log(`   Conversation: ${data.conversation_id}`);
    console.log(`   Session: ${data.session_id}`);
    console.log(`   Status: ${data.status}`);
    console.log(`   Session Type: ${data.session_type}`);
    if (data.data && data.data.data) {
        const answerData = JSON.stringify(data.data.data, null, 2);
        console.log(`   Answer: ${answerData}`);
    }
    console.log('');
    
    
    if (!isFirstAnswerReceived) {
        isFirstAnswerReceived = true;
        console.log('   Sending follow-up query...');
        setTimeout(() => {
            sendQuery();
        }, 2000);
    }else{
        // Flow completed successfully
        console.log('🎉 Complete flow executed successfully!');
        console.log('   ✅ Created conversation');
        console.log('   ✅ Sent query');
        console.log('   ✅ Connected to conversation');
        console.log('   ✅ Received answer');
        console.log('');
        console.log('🎉 Disconnecting from Socket.IO server...');
        socket.disconnect();
        process.exit(0);
    }
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Socket.IO client...');
    socket.disconnect();
    process.exit(0);
});

console.log('🚀 Socket.IO client started. Waiting for connection...');
console.log('Press Ctrl+C to exit');
console.log(''); 