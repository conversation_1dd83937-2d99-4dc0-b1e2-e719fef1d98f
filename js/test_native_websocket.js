// Native WebSocket client test (no Socket.IO)
// This connects directly to a WebSocket endpoint without Socket.IO protocol

const WebSocket = require('ws');

// Test connecting to different WebSocket endpoints
const endpoints = [
    //'ws://localhost:5082/ws',
    //'ws://localhost:5082/websocket',
    'wss://chat.dev.layernext.ai/ws/conversation/'
];

function testWebSocketConnection(url) {
    console.log(`\n🔌 Attempting to connect to: ${url}`);
    
    const ws = new WebSocket(url);
    
    ws.on('open', function open() {
        console.log(`✅ Connected to ${url}`);

        // Send different types of test messages based on the endpoint
        if (url.includes('/ws/conversation/')) {
            // Test conversation endpoint with various message types
            setTimeout(() => {
                const pingMessage = JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
                ws.send(pingMessage);
                console.log(`📤 Sent ping: ${pingMessage}`);
            }, 1000);

            setTimeout(() => {
                const echoMessage = JSON.stringify({
                    type: 'echo',
                    message: 'Test echo message',
                    timestamp: new Date().toISOString()
                });
                ws.send(echoMessage);
                console.log(`📤 Sent echo: ${echoMessage}`);
            }, 2000);

            setTimeout(() => {
                const broadcastMessage = JSON.stringify({
                    type: 'broadcast_request',
                    message: 'Hello to all connected clients!',
                    timestamp: new Date().toISOString()
                });
                ws.send(broadcastMessage);
                console.log(`📤 Sent broadcast request: ${broadcastMessage}`);
            }, 3000);

        } else {
            // Send a simple test message for other endpoints
            const testMessage = JSON.stringify({
                type: 'test',
                message: 'Hello from native WebSocket client',
                timestamp: new Date().toISOString()
            });

            ws.send(testMessage);
            console.log(`📤 Sent: ${testMessage}`);
        }
    });
    
    ws.on('message', function message(data) {
        const messageStr = data.toString();
        console.log(`📥 Received from ${url}:`, messageStr);

        // Try to parse and display analysis step progress messages nicely
        try {
            const messageObj = JSON.parse(messageStr);

            if (messageObj.data_type === 'progress_bar' && messageObj.data && messageObj.data.data) {
                const progressData = messageObj.data.data;
                const timestamp = new Date().toLocaleTimeString();

                console.log(`\n🔄 [${timestamp}] Analysis Progress Update:`);
                console.log(`   📋 Title: ${progressData.title}`);
                console.log(`   📊 Progress: ${progressData.progress}% [${'█'.repeat(Math.floor(progressData.progress/5))}${'░'.repeat(20-Math.floor(progressData.progress/5))}]`);
                console.log(`   🔧 Current Step: ${progressData.currentStep}`);
                console.log(`   🌊 Streaming: ${progressData.isStreaming ? '✅' : '❌'}`);
                console.log(`   💬 Conversation: ${messageObj.conversation_id}`);
                console.log(`   🎯 Session: ${messageObj.session_id}`);
                console.log(`   📝 Session Type: ${messageObj.session_type}`);
                console.log(`   ⚡ Status: ${messageObj.status}`);

            } else if (messageObj.type === 'connection_established') {
                console.log(`\n✅ Connection established successfully!`);
                console.log(`   🆔 Connection ID: ${messageObj.connection_id}`);
                console.log(`   💬 Message: ${messageObj.message}`);

            } else if (messageObj.type === 'pong') {
                console.log(`💓 Received pong response`);

            } else {
                console.log(`📨 Other message type: ${messageObj.type || 'unknown'}`);
            }
        } catch (e) {
            // Not JSON or different format, already logged above
        }
    });
    
    ws.on('error', function error(err) {
        console.log(`❌ Error connecting to ${url}:`, err.message);
    });
    
    ws.on('close', function close(code, reason) {
        console.log(`🔌 Connection to ${url} closed. Code: ${code}, Reason: ${reason}`);
    });
    
    // Keep connection alive for /ws/conversation/ endpoint
    if (url.includes('/ws/conversation/')) {
        console.log(`🔄 Keeping connection alive for ${url} - listening for analysis step messages...`);
        console.log('   Press Ctrl+C to exit');

        // Send periodic ping to keep connection alive
        const pingInterval = setInterval(() => {
            if (ws.readyState === WebSocket.OPEN) {
                const pingMessage = JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
                ws.send(pingMessage);
                console.log(`💓 Sent keepalive ping`);
            } else {
                clearInterval(pingInterval);
            }
        }, 30000); // Send ping every 30 seconds

        // Handle connection close
        ws.on('close', () => {
            clearInterval(pingInterval);
        });

    } else {
        // Close connection after 5 seconds for other endpoints
        setTimeout(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        }, 5000);
    }
}

// Test each endpoint
console.log('🚀 Starting native WebSocket connection tests...');

// First test all endpoints briefly
endpoints.forEach((endpoint, index) => {
    if (!endpoint.includes('/ws/conversation/')) {
        setTimeout(() => {
            testWebSocketConnection(endpoint);
        }, index * 1000); // Stagger the connection attempts
    }
});

// Then connect to /ws/conversation/ and keep it alive
setTimeout(() => {
    console.log('\n🎯 Now connecting to /ws/conversation/ for persistent listening...');
    testWebSocketConnection('wss://chat.dev.layernext.ai/ws/conversation/');
}, 3000);

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down WebSocket test client...');
    process.exit(0);
});

// Keep the process alive indefinitely for /ws/conversation/ testing
console.log('\n📝 This client will stay connected to /ws/conversation/ to receive analysis step messages');
console.log('📝 Run analysis queries in your application to see real-time progress updates');
console.log('📝 Or run: python test_websocket_analysis_steps.py to simulate analysis steps');
console.log('📝 Press Ctrl+C to exit\n');
