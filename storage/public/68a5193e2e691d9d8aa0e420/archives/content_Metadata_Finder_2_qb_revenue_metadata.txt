Metadata of all data sources:

````
[
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "invoice_line": {
        "table_name": "invoice_line",
        "fields": {
          "invoice_id": {
            "name": "invoice_id",
            "description": "Foreign key referencing the parent invoice for this line item, uniquely identifying the invoice to which the line belongs.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "206",
              "208",
              "1621",
              "2200",
              "2648",
              "and 1682 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Zero-based position indicating the order of this line item within its invoice, ensuring uniqueness in combination with 'invoice_id'.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "line_num": {
            "name": "line_num",
            "description": "Optional display-oriented line number, representing the human-readable sequence of the line as shown on the invoice document.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "description": {
            "name": "description",
            "description": "Free-text details or notes specific to this invoice line, such as product or service descriptions; may be blank or repeated across lines.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Permanent Placement",
              "placement of Marcia Baptista",
              "Yulia Sorokotiaha (80 hours)",
              "Rent & Admin",
              "placement of Nimali Perera",
              "and 1634 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Total monetary value for this invoice line, representing the extended or subtotal amount depending on line type.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "2500",
              "2880",
              "3123",
              "4500",
              "6000",
              "and 731 more..."
            ]
          },
          "detail_type": {
            "name": "detail_type",
            "description": "Categorizes the nature of the line item, distinguishing among group lines, subtotals, and individual sales items to determine relevant fields.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "SubTotalLineDetail",
              "SalesItemLineDetail",
              "GroupLineDetail"
            ]
          },
          "sales_item_unit_price": {
            "name": "sales_item_unit_price",
            "description": "Price charged per unit for the sales item on this line, used when the line represents a sales item detail.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "36",
              "100",
              "130",
              "2500",
              "3123",
              "and 598 more..."
            ]
          },
          "sales_item_item_id": {
            "name": "sales_item_item_id",
            "description": "Foreign key referencing the specific item or product sold on this line, linking to the 'item' table when applicable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7"
            ]
          },
          "sales_item_class_id": {
            "name": "sales_item_class_id",
            "description": "Optional foreign key referencing the associated class for this sales item, used for classification in accounting or reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009753",
              "1009755"
            ]
          },
          "sales_item_quantity": {
            "name": "sales_item_quantity",
            "description": "Number of units or quantity of service provided on this line, relevant only for sales item detail lines.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "10",
              "12",
              "20",
              "30",
              "and 87 more..."
            ]
          },
          "sales_item_account_id": {
            "name": "sales_item_account_id",
            "description": "Foreign key linking this sales item line to its related account in the 'account' table for financial categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "10",
              "31",
              "52",
              "75",
              "129",
              "137",
              "141"
            ]
          },
          "sales_item_tax_code_id": {
            "name": "sales_item_tax_code_id",
            "description": "Foreign key indicating the tax code applied to this sales item line, determining tax calculation and treatment.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5"
            ]
          },
          "bundle_quantity": {
            "name": "bundle_quantity",
            "description": "Quantity of bundled groups represented by this line, present only if the line corresponds to a bundled offering.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "57",
              "70",
              "71",
              "72",
              "and 13 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp of the most recent data synchronization for this record, used for tracking ETL data currency and audit purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:56.628000+00:00",
              "2025-08-06 22:09:56.629000+00:00",
              "2025-08-06 22:09:56.308000+00:00",
              "2025-08-06 22:09:56.309000+00:00",
              "2025-08-06 22:09:56.587000+00:00",
              "and 256 more..."
            ]
          }
        }
      },
      "journal_entry_line": {
        "table_name": "journal_entry_line",
        "fields": {
          "journal_entry_id": {
            "name": "journal_entry_id",
            "description": "The unique identifier linking this line item to its parent journal entry, ensuring all related lines are grouped under the same accounting transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "16",
              "21",
              "291",
              "1063",
              "and 952 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "A sequential number indicating the specific position of this line within its parent journal entry, distinguishing each line when multiple entries exist for a single journal entry.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 66 more..."
            ]
          },
          "description": {
            "name": "description",
            "description": "A brief text providing context or details about the nature or purpose of this particular debit or credit line within the journal entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "CPP/QPP Employer",
              "EI Employer",
              "Transfer to Hold Co",
              "To clear AR",
              "Red River Valley Mutual MBNA",
              "and 1041 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The financial value associated with this line, representing the debit or credit amount recorded for the corresponding account.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "5",
              "10",
              "17",
              "100",
              "and 2526 more..."
            ]
          },
          "posting_type": {
            "name": "posting_type",
            "description": "Specifies whether the line item represents a debit or a credit transaction, fundamental for double-entry accounting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Debit",
              "Credit"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "References the customer related to this line, identifying client-specific transactions such as accounts receivable; null if not customer-related.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "9",
              "11",
              "14",
              "24",
              "29",
              "and 23 more..."
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "References the vendor associated with this line, used to identify vendor-specific transactions such as accounts payable; null if not vendor-related.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "35",
              "50",
              "184",
              "225",
              "228",
              "259",
              "293",
              "354",
              "357",
              "415"
            ]
          },
          "tax_applicable_on": {
            "name": "tax_applicable_on",
            "description": "Indicates the transaction context ('Sales' or 'Purchase') in which tax is applied for this line, clarifying whether tax pertains to a sale or purchase.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Sales",
              "Purchase"
            ]
          },
          "tax_amount": {
            "name": "tax_amount",
            "description": "The portion of the line amount attributed to tax, included only when tax is relevant to the transaction.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "225",
              "520",
              "600",
              "675",
              "and 43 more..."
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "References a classification or category assigned to this line, supporting segmentation and detailed financial reporting; links to the class table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "References the specific ledger account impacted by this line item, identifying whether the entry affects assets, liabilities, income, or expenses.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "19",
              "30",
              "31",
              "33",
              "34",
              "and 107 more..."
            ]
          },
          "tax_code_id": {
            "name": "tax_code_id",
            "description": "References the tax code applied to this line for reporting and compliance purposes, linking to the tax_code table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "The timestamp recording when this record was last updated or synchronized to the database via Fivetran ETL integration.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:00.175000+00:00",
              "2025-08-06 22:10:00.486000+00:00",
              "2025-08-06 22:10:00.484000+00:00",
              "2025-08-06 22:10:00.240000+00:00",
              "2025-08-06 22:10:00.236000+00:00",
              "and 319 more..."
            ]
          }
        }
      },
      "deposit_line": {
        "table_name": "deposit_line",
        "fields": {
          "deposit_id": {
            "name": "deposit_id",
            "description": "Foreign key referencing the 'deposit' table, uniquely identifying the parent deposit for this line item and grouping multiple lines under a single deposit transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "6",
              "7",
              "1827",
              "2187",
              "and 28 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Sequential number representing this line item's position within its parent deposit, ensuring uniqueness together with deposit_id.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1"
            ]
          },
          "description": {
            "name": "description",
            "description": "Optional text field providing specific details or context about the purpose or nature of this individual deposit line item.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "ACCT BAL REBATE",
              "Deposit in Error - paid x2",
              "Mani Reddy RECLAIM E-TFR",
              "Adj",
              "RECLAIM E-TFR ***Gsf View more RECLAIM E-TFR ***Gsf"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value attributed to this deposit line item, indicating the credited or debited amount for this portion of the deposit.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "125",
              "300",
              "21.37",
              "23.91",
              "and 27 more..."
            ]
          },
          "deposit_tax_applicable_on": {
            "name": "deposit_tax_applicable_on",
            "description": "Category specifying if and on what the tax is applicable for this deposit line, present only when tax considerations apply (typically 'Sales').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Sales"
            ]
          },
          "deposit_tax_code_id": {
            "name": "deposit_tax_code_id",
            "description": "Foreign key to the 'tax_code' table, identifying the specific tax code applied to this deposit line when tax is relevant.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2"
            ]
          },
          "deposit_class_id": {
            "name": "deposit_class_id",
            "description": "Foreign key to the 'class' table, optionally indicating the business class or department associated with this deposit line for internal segmentation.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754"
            ]
          },
          "deposit_account_id": {
            "name": "deposit_account_id",
            "description": "Foreign key to the 'account' table, identifying the financial account to which this deposit line is posted.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "19",
              "46",
              "52",
              "67",
              "102",
              "141"
            ]
          },
          "deposit_customer_id": {
            "name": "deposit_customer_id",
            "description": "Foreign key to the 'customer' table, optionally linking this deposit line to a specific customer involved in the transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "71",
              "555",
              "614",
              "621"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp of the most recent synchronization of this record into the database by the Fivetran ETL tool, used for tracking data freshness and lineage.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:37.073000+00:00",
              "2025-08-06 22:09:37.070000+00:00",
              "2025-08-06 22:09:37.071000+00:00",
              "2025-08-06 22:09:37.072000+00:00",
              "2025-08-07 02:50:01.049000+00:00",
              "and 1 more..."
            ]
          }
        }
      },
      "account": {
        "table_name": "account",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated unique identifier for each account record, serving as the primary key within the chart of accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "12",
              "13",
              "14",
              "15",
              "and 189 more..."
            ]
          },
          "currency_id": {
            "name": "currency_id",
            "description": "Identifier linking to the 'currency' table, specifying the account's currency; always 'CAD' (Canadian dollars) in this dataset.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "CAD"
            ]
          },
          "description": {
            "name": "description",
            "description": "Optional free-text field used to provide supplementary notes or context for the account, often shared among related accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Any discounts you give your customers on your products or services.",
              "Costs for getting the supplies you use to make your products delivered to you.",
              "Costs for supplies you buy to complete a job or provide a service (not to make products you sell).",
              "Costs for liability insurance for your business.",
              "Money you pay to rent or lease things like office space and storage.",
              "and 32 more..."
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "Globally unique hierarchical path for the account, detailing its position and lineage within nested account structures.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5785",
              "Corporate Tax Receivable",
              "Suspense-Sherry Fontaine (deleted)",
              "Accounts Receivable (A/R):Allowance For Doubtful Accounts",
              "Accounts Receivable (A/R):Accounts Receivable",
              "and 189 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Categorical label denoting the functional group of the account (e.g., 'Accounts Payable'), used for operational grouping and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Current Asset",
              "Other Expense",
              "Other Current Liability",
              "Cost of Goods Sold",
              "Accounts Receivable",
              "Fixed Asset",
              "Credit Card",
              "Bank",
              "Accounts Payable",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "name": {
            "name": "name",
            "description": "User-facing display name for the account, nearly unique and designed for selection in application interfaces.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5785",
              "Insurance",
              "Corporate Tax Receivable",
              "Allowance For Doubtful Accounts",
              "Accounts Receivable",
              "and 187 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "Boolean indicator showing if the account is active (true) or inactive/deleted (false) for filtering and soft deletion.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "classification": {
            "name": "classification",
            "description": "High-level financial category (such as 'Asset', 'Liability', 'Expense', etc.) that determines the account's grouping on financial statements.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Asset",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "sub_account": {
            "name": "sub_account",
            "description": "Boolean flag indicating whether the account is a sub-account (true) nested under a parent, or a top-level account (false).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Fine-grained subtype providing additional specificity within the account_type category, supporting more detailed reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "OfficeGeneralAdministrativeExpenses",
              "OtherMiscellaneousExpense",
              "OtherMiscellaneousServiceCost",
              "OtherCurrentAssets",
              "OtherCurrentLiabilities",
              "and 53 more..."
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp recording when the account record was initially created, supporting auditing and lifecycle analysis.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-01-22 13:39:45+00:00",
              "2018-01-22 13:39:44+00:00",
              "2018-02-24 17:32:41+00:00",
              "2018-02-24 17:41:19+00:00",
              "2018-03-30 19:58:23+00:00",
              "and 132 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp of the most recent update to the account record, used for change tracking and audit trails.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 15:57:47+00:00",
              "2021-02-17 23:02:54+00:00",
              "2018-03-30 19:58:23+00:00",
              "2018-03-30 15:48:38+00:00",
              "2018-03-30 19:37:59+00:00",
              "and 179 more..."
            ]
          },
          "balance_with_sub_accounts": {
            "name": "balance_with_sub_accounts",
            "description": "Numeric value representing the account's total balance, inclusive of all direct and indirect sub-accounts for consolidated financial views.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "300",
              "3000",
              "20000",
              "and 32 more..."
            ]
          },
          "balance": {
            "name": "balance",
            "description": "Numeric value of the account's standalone balance, excluding any amounts from sub-accounts.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "300",
              "3000",
              "20000",
              "and 31 more..."
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "Optional internal or external account code, numeric or alphanumeric, used for integrations or as an alternate reference.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1050",
              "1055",
              "1060",
              "1200",
              "1201",
              "and 123 more..."
            ]
          },
          "parent_account_id": {
            "name": "parent_account_id",
            "description": "Reference to the 'id' of the parent account, establishing hierarchical relationships for sub-accounts; null for top-level accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "58",
              "63",
              "66",
              "73",
              "76",
              "109",
              "112",
              "116",
              "120",
              "152",
              "178"
            ]
          },
          "tax_code_id": {
            "name": "tax_code_id",
            "description": "Optional reference to the default tax code for the account, linking to the 'tax_code' table to support tax calculations and compliance.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "7",
              "8"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "System-managed timestamp marking the last synchronization of this record by Fivetran, used for data pipeline monitoring.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:29.070000+00:00",
              "2025-08-06 22:09:29.047000+00:00",
              "2025-08-06 22:09:29.053000+00:00",
              "2025-08-06 22:09:29.051000+00:00",
              "2025-08-06 22:09:29.059000+00:00",
              "and 39 more..."
            ]
          }
        }
      },
      "class": {
        "table_name": "class",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string identifier that serves as the primary key for each business division (class) record in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "The complete, descriptive name for the business division, providing an unambiguous and unique label for reporting and categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "People Culture",
              "Executive",
              "Accounting Operations",
              "Finance",
              "Global Advisory",
              "Information Technology"
            ]
          },
          "name": {
            "name": "name",
            "description": "A concise, human-readable name for the business division, uniquely identifying the class in user interfaces or selection lists.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "People Culture",
              "Executive",
              "Accounting Operations",
              "Finance",
              "Global Advisory",
              "Information Technology"
            ]
          },
          "active": {
            "name": "active",
            "description": "A boolean flag indicating whether the business division is currently operational (TRUE) or inactive/deprecated (FALSE).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp capturing the exact UTC date and time when the business division record was first created in the system.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-03-23 18:18:52+00:00",
              "2025-03-23 18:19:48+00:00",
              "2025-03-23 18:19:31+00:00",
              "2025-03-23 18:19:13+00:00",
              "2025-04-03 15:53:51+00:00",
              "and 1 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp marking the most recent modification to the business division record, supporting audit and change tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-04-03 15:54:08+00:00",
              "2025-03-23 18:19:48+00:00",
              "2025-03-23 18:19:31+00:00",
              "2025-03-23 18:19:13+00:00",
              "2025-04-03 15:53:51+00:00",
              "and 1 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp automatically maintained by Fivetran, indicating when the record was last synchronized from the source to the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "All distinct values": [
              "2025-08-06 22:09:33.269000+00:00",
              "2025-08-07 02:49:58.198000+00:00",
              "2025-08-07 02:49:58.197000+00:00"
            ]
          }
        }
      },
      "invoice": {
        "table_name": "invoice",
        "fields": {
          "id": {
            "name": "id",
            "description": "Primary key uniquely identifying each invoice record in the system.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "71",
              "77",
              "128",
              "130",
              "132",
              "and 1682 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "System-assigned invoice number used for client communication and accounting, unique within the invoice table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1003",
              "1006",
              "1035",
              "1036",
              "1037",
              "and 1682 more..."
            ]
          },
          "balance": {
            "name": "balance",
            "description": "The remaining unpaid amount on the invoice, reflecting what the customer still owes.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "5733",
              "8400",
              "8820",
              "10500",
              "and 63 more..."
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp capturing when the invoice record was initially created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-30 19:28:22+00:00",
              "2018-03-30 19:23:24+00:00",
              "2018-03-30 19:26:21+00:00",
              "2018-03-30 20:08:44+00:00",
              "2018-03-30 19:25:22+00:00",
              "and 1682 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp of the latest modification made to the invoice record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-04-16 16:01:16+00:00",
              "2024-09-16 15:51:18+00:00",
              "2024-05-24 12:03:19+00:00",
              "2023-10-23 13:36:47+00:00",
              "2021-05-14 13:53:40+00:00",
              "and 1507 more..."
            ]
          },
          "total_tax": {
            "name": "total_tax",
            "description": "The aggregate tax amount calculated for this invoice, based on taxable items and rates.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "125",
              "144",
              "225",
              "300",
              "and 686 more..."
            ]
          },
          "shipping_address_id": {
            "name": "shipping_address_id",
            "description": "Foreign key referencing the shipping address for this invoice in the address table; may be null if shipping is not applicable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "23",
              "26",
              "40",
              "46",
              "and 200 more..."
            ]
          },
          "billing_address_id": {
            "name": "billing_address_id",
            "description": "Foreign key referencing the billing address for this invoice in the address table; may be null if not specified.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "23",
              "26",
              "40",
              "46",
              "and 272 more..."
            ]
          },
          "due_date": {
            "name": "due_date",
            "description": "The date by which the customer is expected to pay the invoice in full.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-11-30",
              "2019-02-01",
              "2022-06-01",
              "2018-07-18",
              "2021-12-01",
              "and 856 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The grand total billed on the invoice, including all line items and taxes, representing the full amount due.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1050",
              "2625",
              "3024",
              "4725",
              "6300",
              "and 724 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official accounting date assigned to the invoice, used for reporting and ledger entries.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-10-31",
              "2019-01-02",
              "2022-05-22",
              "2022-07-10",
              "2021-11-01",
              "and 867 more..."
            ]
          },
          "sales_term_id": {
            "name": "sales_term_id",
            "description": "Foreign key linking to the payment terms (e.g., Net 30) in the term table, which dictates when payment is due.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "4",
              "6"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Foreign key linking to the customer associated with this invoice, identifying the billed party.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "7",
              "9",
              "10",
              "11",
              "14",
              "and 370 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp marking the most recent synchronization of this record by the Fivetran data integration system.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:56.628000+00:00",
              "2025-08-06 22:09:56.629000+00:00",
              "2025-08-06 22:09:56.308000+00:00",
              "2025-08-06 22:09:56.309000+00:00",
              "2025-08-06 22:09:56.288000+00:00",
              "and 255 more..."
            ]
          }
        }
      },
      "journal_entry": {
        "table_name": "journal_entry",
        "fields": {
          "id": {
            "name": "id",
            "description": "The primary key string uniquely identifying each journal entry header in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5",
              "8",
              "9",
              "13",
              "15",
              "and 952 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "An optional string referencing the external or accounting document number tied to the journal entry, which may be non-unique or missing.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5",
              "7",
              "11",
              "12",
              "13",
              "and 811 more..."
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "The precise timestamp indicating when the journal entry record was first created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2019-10-01 08:50:11+00:00",
              "2019-08-01 08:48:46+00:00",
              "2019-12-01 09:51:57+00:00",
              "2018-03-01 09:29:25+00:00",
              "2019-09-01 08:49:03+00:00",
              "and 944 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "The latest timestamp reflecting when any modification was last saved to the journal entry record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-01 09:29:25+00:00",
              "2022-11-20 18:22:49+00:00",
              "2018-02-24 18:31:52+00:00",
              "2018-03-17 17:59:14+00:00",
              "2018-02-24 19:30:20+00:00",
              "and 950 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The date, without time, representing when the journal entry is considered effective for accounting and reporting purposes.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2021-10-31",
              "2020-10-31",
              "2022-10-31",
              "2017-11-01",
              "and 625 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "The timestamp capturing the most recent Fivetran synchronization event for this journal entry record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:00.326000+00:00",
              "2025-08-06 22:10:00.378000+00:00",
              "2025-08-06 22:10:00.351000+00:00",
              "2025-08-06 22:10:00.266000+00:00",
              "2025-08-06 22:10:00.256000+00:00",
              "and 319 more..."
            ]
          }
        }
      },
      "deposit": {
        "table_name": "deposit",
        "fields": {
          "id": {
            "name": "id",
            "description": "Primary key; uniquely identifies each deposit transaction record in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "6",
              "7",
              "1827",
              "2187",
              "and 28 more..."
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp marking when the deposit record was first created in the system for audit and historical purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-05-17 15:53:44+00:00",
              "2020-08-07 17:45:11+00:00",
              "2020-08-07 20:37:57+00:00",
              "2020-08-07 20:42:17+00:00",
              "2020-10-19 18:12:21+00:00",
              "and 28 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp showing the most recent modification to the deposit record, tracking change history.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-05-17 15:53:44+00:00",
              "2020-08-07 17:45:11+00:00",
              "2020-08-07 20:37:57+00:00",
              "2020-08-07 20:42:17+00:00",
              "2020-10-19 18:12:21+00:00",
              "and 28 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Total monetary value of the deposit transaction, stored with high precision for financial accuracy.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "125",
              "300",
              "21.37",
              "0.95",
              "and 26 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "Calendar date when the deposit transaction officially took place, used for accounting and reconciliation.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-10-31",
              "2025-05-30",
              "2020-03-31",
              "2021-01-29",
              "2020-08-31",
              "and 25 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Foreign key referencing the destination account that received the deposit, linking to account details.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "53",
              "54",
              "55",
              "89",
              "90"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp of the last successful synchronization of this record by the Fivetran ETL integration process.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:37.073000+00:00",
              "2025-08-06 22:09:37.070000+00:00",
              "2025-08-06 22:09:37.071000+00:00",
              "2025-08-06 22:09:37.072000+00:00",
              "2025-08-06 22:09:37.068000+00:00",
              "and 2 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \n\n\nTo compute monthly revenue for 2025, we need all general ledger (GL) transaction tables/columns, especially those capturing amounts, dates, types, and account classifications. The QuickBooks_Reports.quickbooks__general_ledger equivalent is not explicitly named but can be inferred from core transactional and GL-representing tables (e.g., invoice_line, journal_entry_line, deposit_line, etc.). Keys and business rules are found in the metadata, and ER relationships clarify how to join with account/class tables for correct revenue interpretation. Additional tables like account and class are essential for filtering to revenue accounts.  \n\n\n  \n**Table Columns:**  \n\n\n- table: invoice_line  \n  columns: invoice_id (PK, FK), index (PK), amount (BIGNUMERIC), description (STRING), sales_item_account_id (FK), sales_item_class_id (FK), sales_item_tax_code_id (FK), sales_item_item_id (FK), detail_type (STRING), line_num (INT64), sales_item_unit_price (BIGNUMERIC), sales_item_quantity (BIGNUMERIC), bundle_quantity (BIGNUMERIC), _fivetran_synced (TIMESTAMP)  \n\n- table: journal_entry_line  \n  columns: journal_entry_id (PK, FK), index (PK), amount (BIGNUMERIC), posting_type (STRING), account_id (FK), class_id (FK), customer_id (FK), vendor_id (FK), tax_applicable_on (STRING), tax_amount (BIGNUMERIC), tax_code_id (FK), description (STRING), _fivetran_synced (TIMESTAMP)  \n\n- table: deposit_line  \n  columns: deposit_id (PK, FK), index (PK), amount (BIGNUMERIC), deposit_account_id (FK), deposit_class_id (FK), deposit_tax_code_id (FK), deposit_customer_id (FK), description (STRING), deposit_tax_applicable_on (STRING), _fivetran_synced (TIMESTAMP)  \n\n- table: account  \n  columns: id (PK), account_type (STRING), classification (STRING), name (STRING), fully_qualified_name (STRING), description (STRING), active (BOOL), sub_account (BOOL), account_sub_type (STRING), parent_account_id (FK), currency_id (FK), tax_code_id (FK), balance (BIGNUMERIC), balance_with_sub_accounts (BIGNUMERIC), account_number (STRING), created_at (TIMESTAMP), updated_at (TIMESTAMP), _fivetran_synced (TIMESTAMP)  \n\n- table: class  \n  columns: id (PK), name (STRING), fully_qualified_name (STRING), active (BOOL), created_at (TIMESTAMP), updated_at (TIMESTAMP), _fivetran_synced (TIMESTAMP)  \n\n- table: invoice  \n  columns: id (PK), transaction_date (DATE), total_amount (BIGNUMERIC), total_tax (BIGNUMERIC), balance (BIGNUMERIC), sales_term_id (FK), customer_id (FK), doc_number (STRING), created_at (TIMESTAMP), updated_at (TIMESTAMP), billing_address_id (FK), shipping_address_id (FK), due_date (DATE), _fivetran_synced (TIMESTAMP)  \n\n- table: journal_entry  \n  columns: id (PK), transaction_date (DATE), doc_number (STRING), created_at (TIMESTAMP), updated_at (TIMESTAMP), _fivetran_synced (TIMESTAMP)  \n\n- table: deposit  \n  columns: id (PK), transaction_date (DATE), account_id (FK), total_amount (BIGNUMERIC), created_at (TIMESTAMP), updated_at (TIMESTAMP), _fivetran_synced (TIMESTAMP)  \n\n- table: class  \n  columns: id (PK), name (STRING), fully_qualified_name (STRING), active (BOOL)  \n\n**Business rules:**  \n- transaction_id: Links to parent transaction (e.g., invoice_id, journal_entry_id, deposit_id).  \n- transaction_type: Not a direct column but can be inferred from the source table/type (e.g., \"Invoice\", \"Journal Entry\", \"Deposit\").  \n- adjusted_amount: Calculated from amount columns, may require sign adjustment based on posting_type (Credit/Debit) and account classification.  \n- account_class: Found in account.classification or account.account_type fields.  \n- Only lines/accounts with account.classification = 'Revenue' or account.account_type = 'Income' are considered revenue.  \n- transaction_date: Use the transaction_date in the header table (invoice, journal_entry, deposit).  \n\n**ER Diagram JSON (subset for these tables):**  \n```json\n{\n  \"entities\": [\n    {\"name\": \"invoice_line\", \"primaryKeys\": [[\"invoice_id\", \"index\"]]},\n    {\"name\": \"invoice\", \"primaryKeys\": [[\"id\"]]},\n    {\"name\": \"journal_entry_line\", \"primaryKeys\": [[\"journal_entry_id\", \"index\"]]},\n    {\"name\": \"journal_entry\", \"primaryKeys\": [[\"id\"]]},\n    {\"name\": \"deposit_line\", \"primaryKeys\": [[\"deposit_id\", \"index\"]]},\n    {\"name\": \"deposit\", \"primaryKeys\": [[\"id\"]]},\n    {\"name\": \"account\", \"primaryKeys\": [[\"id\"]]},\n    {\"name\": \"class\", \"primaryKeys\": [[\"id\"]]}\n  ],\n  \"relationships\": [\n    {\"from\": \"invoice_line\", \"to\": \"invoice\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"invoice_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"invoice_line\", \"to\": \"account\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"sales_item_account_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"invoice_line\", \"to\": \"class\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"sales_item_class_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"journal_entry_line\", \"to\": \"journal_entry\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"journal_entry_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"journal_entry_line\", \"to\": \"account\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"account_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"journal_entry_line\", \"to\": \"class\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"class_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"deposit_line\", \"to\": \"deposit\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"deposit_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"deposit_line\", \"to\": \"account\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"deposit_account_id\"], \"targetKeys\": [\"id\"]}},\n    {\"from\": \"deposit_line\", \"to\": \"class\", \"type\": \"many-to-one\", \"via\": {\"sourceKeys\": [\"deposit_class_id\"], \"targetKeys\": [\"id\"]}}\n  ]\n}\n```\n\n**Other unrelated tables for context:**  \n- vendor, vendor_credit, bill, bill_line, purchase, purchase_line, purchase_order, purchase_order_line, customer, estimate, estimate_line, employee, payment, payment_line, credit_memo, credit_memo_line, sales_receipt, sales_receipt_line, refund_receipt, refund_receipt_line, tax_code, tax_rate, tax_agency, budget, department, etc.  \n",
    "other_table_columns": {
      "item": [
        "id",
        "fully_qualified_name",
        "name",
        "sync_token",
        "type",
        "sales_tax_included",
        "created_at",
        "updated_at",
        "income_account_id",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [
        "invoice_id",
        "index",
        "line_num",
        "description",
        "amount",
        "unit_price",
        "item_id",
        "class_id",
        "quantity",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [
        "id",
        "sync_token",
        "balance",
        "doc_number",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "due_date",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "estimate_line": [],
      "journal_entry_tax_line": [
        "journal_entry_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "term": [
        "id",
        "name",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [
        "purchase_id",
        "index",
        "amount",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "credit_card_payment_txn": [
        "id",
        "amount",
        "currency_id",
        "created_at",
        "updated_at",
        "transaction_date",
        "bank_account_id",
        "credit_card_account_id",
        "_fivetran_synced"
      ],
      "tax_code": [
        "id",
        "active",
        "description",
        "name",
        "taxable",
        "tax_group",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_type_applicable",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [
        "bill_id",
        "index",
        "bill_payment_id",
        "_fivetran_synced"
      ],
      "transfer": [
        "id",
        "amount",
        "private_note",
        "created_at",
        "updated_at",
        "currency_id",
        "transaction_date",
        "to_account_id",
        "from_account_id",
        "_fivetran_synced"
      ],
      "vendor": [
        "id",
        "family_name",
        "given_name",
        "company_name",
        "active",
        "balance",
        "display_name",
        "print_on_check_name",
        "middle_name",
        "sync_token",
        "billing_address_id",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [
        "invoice_id",
        "index",
        "payment_id",
        "_fivetran_synced"
      ],
      "deposit_line": [
        "detail_type"
      ],
      "tax_agency": [
        "id",
        "display_name",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [
        "id",
        "pay_type",
        "doc_number",
        "sync_token",
        "currency_id",
        "check_print_status",
        "check_bank_account_id",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "account": [
        "sync_token"
      ],
      "address": [
        "id",
        "line_1",
        "line_2",
        "line_3",
        "line_4",
        "city",
        "postal_code",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [
        "id",
        "email_status",
        "doc_number",
        "sync_token",
        "global_tax_calculation",
        "shipping_address_id",
        "billing_address_id",
        "bill_email",
        "customer_memo",
        "total_tax",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "class": [
        "sync_token"
      ],
      "bill_payment_line": [
        "bill_payment_id",
        "index",
        "amount",
        "bill_id",
        "journal_entry_id",
        "_fivetran_synced"
      ],
      "payment_line": [
        "payment_id",
        "index",
        "amount",
        "journal_entry_id",
        "credit_memo_id",
        "invoice_id",
        "_fivetran_synced"
      ],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "journal_entry": [
        "sync_token",
        "private_note"
      ],
      "bill_line": [
        "bill_id",
        "index",
        "description",
        "amount",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "_fivetran_synced"
      ],
      "payment": [
        "id",
        "sync_token",
        "created_at",
        "updated_at",
        "reference_number",
        "unapplied_amount",
        "total_amount",
        "transaction_date",
        "payment_method_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "email_status",
        "print_status",
        "sync_token",
        "global_tax_calculation",
        "delivery_time",
        "billing_email",
        "customer_memo"
      ],
      "purchase": [
        "id",
        "sync_token",
        "credit",
        "doc_number",
        "payment_type",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "customer_id",
        "vendor_id",
        "remit_to_address_id",
        "total_tax",
        "total_amount",
        "transaction_date",
        "account_id",
        "payment_method_id",
        "_fivetran_synced"
      ],
      "customer": [
        "id",
        "family_name",
        "fully_qualified_name",
        "given_name",
        "company_name",
        "display_name",
        "print_on_check_name",
        "title",
        "middle_name",
        "active",
        "sync_token",
        "balance_with_jobs",
        "balance",
        "taxable",
        "created_at",
        "updated_at",
        "email",
        "website",
        "phone_number",
        "mobile_number",
        "shipping_address_id",
        "bill_address_id",
        "default_tax_code_id",
        "sales_term_id",
        "_fivetran_synced"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "id"
      ],
      "credit_memo_line": [
        "credit_memo_id",
        "index",
        "description",
        "amount",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_account_id",
        "_fivetran_synced"
      ],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "payment_method": [
        "id",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [
        "purchase_id",
        "index",
        "amount",
        "description",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "account_expense_customer_id",
        "_fivetran_synced"
      ],
      "deposit": [
        "sync_token",
        "private_note",
        "global_tax_calculation"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Reports",
    "selected_table_column_metadata": {
      "quickbooks__general_ledger": {
        "table_name": "quickbooks__general_ledger",
        "fields": {
          "unique_id": {
            "name": "unique_id",
            "description": "A system-generated primary key that uniquely identifies each individual general ledger line item in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "177761637a1e9da971584f556c12fe54",
              "6121a0bf392766b81cff0d01569de02e",
              "aa7f2f904eb74faf0a38f71b6ce1a147",
              "7701a8814098f05c67dd1908ad4c5cb8",
              "5516b1e25a4d65c8d6d52f614f7eb224",
              "and 25457 more..."
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "An identifier linking this ledger entry to a specific accounting transaction, grouping all related line items under the same transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "16",
              "21",
              "291",
              "1063",
              "and 6559 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The effective date on which the accounting transaction was recognized in the ledger.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2017-10-31",
              "2020-10-31",
              "2017-11-30",
              "and 1932 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "The primary accounting category of the affected account, such as 'Expense', 'Asset', or 'Liability'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable",
              "Expense",
              "Bank",
              "Accounts Payable",
              "Income",
              "Other Current Asset",
              "Other Current Liability",
              "Credit Card",
              "Other Expense",
              "Fixed Asset",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A high-level grouping of the account for financial reporting, such as 'Revenue', 'Asset', 'Expense', 'Liability', or 'Equity'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Expense",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Specifies whether this ledger line is a debit or a credit, defining its effect on the account balance.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "adjusted_amount": {
            "name": "adjusted_amount",
            "description": "The monetary value for this entry after any corrections or adjustments have been applied, which may differ from the original amount.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2500",
              "3123",
              "8000",
              "8400",
              "and 7488 more..."
            ]
          }
        }
      },
      "quickbooks__profit_and_loss": {
        "table_name": "quickbooks__profit_and_loss",
        "fields": {
          "account_class": {
            "name": "account_class",
            "description": "Indicates whether the transaction relates to revenue or expense, providing a primary split for profit and loss categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Revenue"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Specifies the high-level financial category of the account, such as 'Expense', 'Income', 'Other Expense', or 'Other Income', aligning with standard financial statement classifications.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Expense",
              "Other Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides detailed categorization within the account type, describing the specific nature or purpose of the account (e.g., 'OfficeGeneralAdministrativeExpenses').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "OfficeGeneralAdministrativeExpenses",
              "PayrollExpenses",
              "OtherMiscellaneousServiceCost",
              "AdvertisingPromotional",
              "OtherMiscellaneousExpense",
              "ServiceFeeIncome",
              "CostOfLabor",
              "Auto",
              "EntertainmentMeals",
              "BankCharges",
              "OtherPrimaryIncome",
              "Travel",
              "LegalProfessionalFees",
              "BadDebts",
              "UnappliedCashPaymentIncome",
              "Amortization",
              "TaxesPaid",
              "TravelMeals",
              "InterestPaid",
              "InterestEarned",
              "DiscountsRefundsGiven",
              "Insurance"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A system-generated unique identifier for the account, ensuring precise account reference within the accounting software.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "32",
              "and 74 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The human-readable name of the account, such as 'Marketing' or 'Office Space Income', used for clear identification in reports.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Wages & Salaries",
              "EI Expense",
              "Employee Benefits",
              "Temp Payroll",
              "CPP Expense",
              "and 72 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \n\n\nTo compute monthly total revenue for 2025, I focused on the `quickbooks__general_ledger` table, as it directly contains transaction-level data with the necessary columns (adjusted_amount, transaction_date, transaction_type, account_class, transaction_id). I included data types, key columns, and business rules per metadata. Revenue identification may require account_class or account_type. The ER-diagram and a list of other tables provide context as requested.  \n\n\n---  \n\n\n**Table Columns:**  \n\n\n- table: quickbooks__general_ledger  \n  columns:  \n    - unique_id (STRING, Primary Key)  \n    - transaction_id (STRING, Foreign Key for linkage per business rules)  \n    - transaction_date (DATE)  \n    - adjusted_amount (BIGNUMERIC)  \n    - transaction_type (STRING; values: 'debit', 'credit')  \n    - account_class (STRING; values include 'Revenue')  \n    - account_type (STRING; may help further distinguish revenue)  \n\n\n- table: quickbooks__profit_and_loss  \n  columns:  \n    - account_class (STRING; values: 'Revenue', 'Expense')  \n    - account_id (STRING)  \n    - account_name (STRING)  \n    - account_type (STRING; 'Income', 'Other Income' etc.)  \n    - account_sub_type (STRING)  \n\n\n---  \n\n\n**Business Rules & Metadata:**  \n\n\n- `transaction_id`: Links each ledger entry to its source transaction (invoice, bill, deposit, etc.).  \n- `adjusted_amount`: Final calculated monetary value, after corrections/adjustments, for the ledger entry.  \n- `transaction_date`: Effective date of the general ledger entry.  \n- `transaction_type`: Indicates if the line is a 'debit' or 'credit'.  \n- `account_class`: Used to classify as 'Revenue', 'Expense', etc.  \n- Only rows with `account_class = 'Revenue'` should be considered for revenue calculations.  \n- Primary Key: unique_id (quickbooks__general_ledger).  \n- Foreign Keys: transaction_id (links to raw transactions).  \n\n\n---  \n\n\n**ER-diagram JSON for Relevant Tables:**  \n\n\n```json\n{\n  \"entities\": [\n    {\n      \"name\": \"quickbooks__general_ledger\",\n      \"primaryKeys\": [\n        [\"unique_id\"]\n      ]\n    },\n    {\n      \"name\": \"quickbooks__profit_and_loss\",\n      \"primaryKeys\": []\n    }\n  ],\n  \"relationships\": []\n}\n```  \n\n\n---  \n\n\n**Other Unrelated Tables for Context:**  \n\n\n- quickbooks__balance_sheet  \n- quickbooks__ap_ar_enhanced  \n- quickbooks__expenses_sales_enhanced  \n- quickbooks__general_ledger_by_period  \n- quickbooks__cash_flow_statement  \n  \n\n---",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "transaction_source",
        "transaction_id",
        "transaction_line_id",
        "doc_number",
        "transaction_type",
        "transaction_date",
        "item_id",
        "item_quantity",
        "item_unit_price",
        "account_id",
        "account_name",
        "account_sub_type",
        "class_id",
        "customer_id",
        "customer_name",
        "customer_website",
        "vendor_id",
        "vendor_name",
        "billable_status",
        "description",
        "amount",
        "converted_amount",
        "total_amount",
        "total_converted_amount"
      ],
      "quickbooks__cash_flow_statement": [
        "cash_flow_period",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "cash_ending_period",
        "cash_converted_ending_period",
        "account_unique_id",
        "cash_flow_type",
        "cash_flow_ordinal",
        "cash_beginning_period",
        "cash_net_period",
        "cash_converted_beginning_period",
        "cash_converted_net_period"
      ],
      "quickbooks__profit_and_loss": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_number",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__balance_sheet": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__ap_ar_enhanced": [
        "transaction_type",
        "transaction_id",
        "doc_number",
        "transaction_with",
        "customer_vendor_name",
        "customer_vendor_balance",
        "customer_vendor_address_city",
        "customer_vendor_address_line",
        "customer_vendor_website",
        "total_amount",
        "total_converted_amount",
        "current_balance",
        "due_date",
        "is_overdue",
        "days_overdue",
        "initial_payment_date",
        "recent_payment_date",
        "total_current_payment",
        "total_current_converted_payment"
      ],
      "quickbooks__general_ledger_by_period": [
        "account_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_class",
        "class_id",
        "financial_statement_helper",
        "date_year",
        "period_first_day",
        "period_last_day",
        "period_net_change",
        "period_beginning_balance",
        "period_ending_balance",
        "period_net_converted_change",
        "period_beginning_converted_balance",
        "period_ending_converted_balance",
        "account_ordinal"
      ],
      "quickbooks__general_ledger": [
        "transaction_index",
        "customer_id",
        "vendor_id",
        "amount",
        "account_id",
        "class_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_sub_type",
        "financial_statement_helper",
        "account_current_balance",
        "transaction_source",
        "account_transaction_type",
        "created_at",
        "updated_at",
        "adjusted_converted_amount",
        "running_balance",
        "running_converted_balance"
      ]
    }
  }
]
````