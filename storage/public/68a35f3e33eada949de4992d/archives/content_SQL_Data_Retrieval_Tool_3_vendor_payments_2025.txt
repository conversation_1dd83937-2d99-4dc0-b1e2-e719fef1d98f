SQL Query:

````
SELECT
  BP.id AS payment_id,
  BP.transaction_date AS payment_date,
  BL.amount AS payment_amount,
  BL.bill_id AS bill_id,
  B.doc_number AS bill_doc_number,
  B.transaction_date AS bill_date,
  V.display_name AS vendor_name
FROM
  bill_payment_line BL
INNER JOIN
  bill_payment BP ON BL.bill_payment_id = BP.id
INNER JOIN
  bill B ON BL.bill_id = B.id
INNER JOIN
  vendor V ON B.vendor_id = V.id
WHERE
  BP.transaction_date BETWEEN '2025-05-15' AND '2025-07-15'
ORDER BY
  BP.transaction_date ASC;
````

Data retrieved successfully:

|   payment_id | payment_date            |   payment_amount |   bill_id | bill_doc_number   | bill_date               | vendor_name   |
|-------------:|:------------------------|-----------------:|----------:|:------------------|:------------------------|:--------------|
|         6861 | {'value': '2025-06-02'} |          5254.54 |      6859 |                   | {'value': '2025-06-02'} | Canada Life   |