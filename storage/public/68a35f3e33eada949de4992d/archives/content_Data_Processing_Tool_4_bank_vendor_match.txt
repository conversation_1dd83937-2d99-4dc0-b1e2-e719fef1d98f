Python code:

```python
#!/usr/bin/env python3
import pandas as pd
import numpy as np
from metalake import load_data
from datetime import datetime
import os

chat_id = "68a35e3233eada949de498e1"

# Filenames
data_bank = "TD_bank_payments_Expenses_allrows.dat"
data_purch = "vendor_purchase_expenses_2025Q2.dat"
data_bill = "bill_payment_lines_2025_window.dat"

csv_path = "files/bank_to_vendor_match.csv"
dat_path = "files/bank_to_vendor_match.dat"

# --- 1. Load all datasets ---
df_bank = load_data(chat_id, data_bank)
df_purch = load_data(chat_id, data_purch)
df_bill = load_data(chat_id, data_bill)

def parse_purchase_date(val):
    if isinstance(val, dict) and 'value' in val:
        return val['value']
    return val

def parse_vendor_name(val):
    return str(val).strip().lower() if pd.notnull(val) else ''

def vendor_token_set(name):
    import re
    tokens = re.split(r'\W+', str(name).lower())
    return set([t for t in tokens if t])

def vendor_similarity(a, b):
    set_a = vendor_token_set(a)
    set_b = vendor_token_set(b)
    if not set_a or not set_b:
        return 0.0
    return len(set_a & set_b) / len(set_a | set_b)

if df_bank.empty or df_purch.empty or df_bill.empty:
    print("One or more datasets are empty. Please check the data files.")
else:
    # --- 2. Normalize and union vendor purchase and bill payment ---
    purch = pd.DataFrame({
        'matched_type': 'purchase',
        'matched_id': df_purch['purchase_id'],
        'vendor_name': df_purch['vendor_name'],
        'matched_date': df_purch['purchase_date'].apply(parse_purchase_date),
        'matched_amount': pd.to_numeric(df_purch['purchase_total_amt'], errors='coerce')
    })
    bill = pd.DataFrame({
        'matched_type': 'bill_payment',
        'matched_id': df_bill['payment_id'],
        'vendor_name': df_bill['vendor_name'],
        'matched_date': df_bill['payment_date'].apply(parse_purchase_date),
        'matched_amount': pd.to_numeric(df_bill['payment_amount'], errors='coerce')
    })
    consolidated = pd.concat([purch, bill], ignore_index=True)
    consolidated['matched_date'] = pd.to_datetime(consolidated['matched_date'], errors='coerce')
    consolidated['vendor_name_norm'] = consolidated['vendor_name'].apply(parse_vendor_name)

    # --- 3. Prepare bank data ---
    df_bank = df_bank.copy()
    bank_date_col = 'Date'
    bank_desc_col = 'Payee'
    bank_amount_col = 'Total'
    df_bank['bank_tx_date'] = pd.to_datetime(df_bank[bank_date_col], errors='coerce')
    df_bank['bank_tx_description'] = df_bank[bank_desc_col].astype(str)
    df_bank['bank_tx_amount'] = pd.to_numeric(df_bank[bank_amount_col], errors='coerce')
    df_bank['bank_tx_description_norm'] = df_bank['bank_tx_description'].apply(parse_vendor_name)

    # --- 4. Match logic ---
    results = []
    for idx, row in df_bank.iterrows():
        best = None
        best_score = -1
        best_fields = None
        for _, cand in consolidated.iterrows():
            vendor_sim = vendor_similarity(row['bank_tx_description_norm'], cand['vendor_name_norm'])
            # Ensure numeric for amount calculations
            bank_amt = row['bank_tx_amount'] if pd.notna(row['bank_tx_amount']) else np.nan
            cand_amt = cand['matched_amount'] if pd.notna(cand['matched_amount']) else np.nan
            if pd.isna(bank_amt) or pd.isna(cand_amt):
                amt_diff = np.nan
                rel_diff = np.nan
            else:
                amt_diff = abs(float(bank_amt) - float(cand_amt))
                rel_diff = (amt_diff / float(cand_amt)) if cand_amt != 0 else np.nan
            bank_date = row['bank_tx_date'] if pd.notna(row['bank_tx_date']) else pd.NaT
            cand_date = cand['matched_date'] if pd.notna(cand['matched_date']) else pd.NaT
            if pd.isna(bank_date) or pd.isna(cand_date):
                date_diff = np.nan
            else:
                date_diff = abs((bank_date - cand_date).days)
            # Combine into a match_score (0-100 scale)
            score_vendor = vendor_sim * 100
            score_amt = max(0, 100 - min(100, (amt_diff if not np.isnan(amt_diff) else 100) * 10))
            score_date = max(0, 100 - min(100, (date_diff if not np.isnan(date_diff) else 100) * 5))
            match_score = 0.5 * score_vendor + 0.4 * score_amt + 0.1 * score_date
            if match_score > best_score:
                best_score = match_score
                best_fields = dict(
                    matched_type=cand['matched_type'],
                    matched_id=cand['matched_id'],
                    vendor_name=cand['vendor_name'],
                    matched_date=cand['matched_date'],
                    matched_amount=cand['matched_amount'],
                    amount_diff=amt_diff,
                    rel_diff=rel_diff,
                    date_diff_days=date_diff,
                    match_score=match_score
                )
        # Accept as match if match_score >= 60 and amount_diff <=5.00 OR rel_diff<=2% and date_diff<=10 days
        is_match = False
        if best_fields is not None:
            amt_cond = (not pd.isna(best_fields['amount_diff']) and best_fields['amount_diff'] <= 5.00)
            rel_date_cond = (
                not pd.isna(best_fields['rel_diff']) and best_fields['rel_diff'] <= 0.02 and
                not pd.isna(best_fields['date_diff_days']) and best_fields['date_diff_days'] <= 10
            )
            if (best_fields['match_score'] >= 60 and amt_cond) or rel_date_cond:
                is_match = True
        if is_match:
            results.append({
                'bank_tx_date': row['bank_tx_date'].strftime('%Y-%m-%d') if pd.notnull(row['bank_tx_date']) else '',
                'bank_tx_description': row['bank_tx_description'],
                'bank_tx_amount': f"{row['bank_tx_amount']:.2f}" if not pd.isna(row['bank_tx_amount']) else '',
                'matched_type': best_fields['matched_type'],
                'matched_id': best_fields['matched_id'],
                'vendor_name': best_fields['vendor_name'],
                'matched_date': best_fields['matched_date'].strftime('%Y-%m-%d') if pd.notnull(best_fields['matched_date']) else '',
                'matched_amount': f"{best_fields['matched_amount']:.2f}" if not pd.isna(best_fields['matched_amount']) else '',
                'amount_diff': f"{best_fields['amount_diff']:.2f}" if not pd.isna(best_fields['amount_diff']) else '',
                'date_diff_days': int(best_fields['date_diff_days']) if not pd.isna(best_fields['date_diff_days']) else '',
                'match_score': f"{best_fields['match_score']:.1f}"
            })
        else:
            results.append({
                'bank_tx_date': row['bank_tx_date'].strftime('%Y-%m-%d') if pd.notnull(row['bank_tx_date']) else '',
                'bank_tx_description': row['bank_tx_description'],
                'bank_tx_amount': f"{row['bank_tx_amount']:.2f}" if not pd.isna(row['bank_tx_amount']) else '',
                'matched_type': '',
                'matched_id': '',
                'vendor_name': '',
                'matched_date': '',
                'matched_amount': '',
                'amount_diff': '',
                'date_diff_days': '',
                'match_score': f"{best_fields['match_score']:.1f}" if best_fields else ''
            })
    # --- 5. Output CSV and .dat ---
    result_df = pd.DataFrame(results)
    result_df.to_csv(csv_path, index=False)
    result_df.to_csv(dat_path, index=False)

    # --- 6. Inline summary ---
    total = len(result_df)
    matched = (result_df['matched_type'] != '').sum()
    unmatched = total - matched
    avg_score = result_df.loc[result_df['matched_type'] != '', 'match_score'].astype(float).mean() if matched > 0 else 0.0
    summary = pd.DataFrame({
        'Metric': ['Total bank rows', 'Matched', 'Unmatched', 'Average match_score (matched)'],
        'Value': [total, matched, unmatched, f"{avg_score:.1f}"]
    })
    print("\n**Bank to Vendor Match Summary:**")
    print(summary.to_markdown(index=False))
    print("\n**Preview of Matched Results:**")
    print(result_df.head(5).to_markdown(index=False))

````

Output:

**Bank to Vendor Match Summary:**
| Metric                        |   Value |
|:------------------------------|--------:|
| Total bank rows               |    95   |
| Matched                       |    85   |
| Unmatched                     |    10   |
| Average match_score (matched) |    99.4 |

**Preview of Matched Results:**
| bank_tx_date   | bank_tx_description   |   bank_tx_amount | matched_type   |   matched_id | vendor_name        | matched_date   |   matched_amount |   amount_diff |   date_diff_days |   match_score |
|:---------------|:----------------------|-----------------:|:---------------|-------------:|:-------------------|:---------------|-----------------:|--------------:|-----------------:|--------------:|
| 2025-06-30     | Tuxedo Golf Course    |            28.8  | purchase       |         6935 | Tuxedo Golf Course | 2025-06-30     |            28.8  |             0 |                0 |           100 |
| 2025-06-30     | Tuxedo Golf Course    |            27    | purchase       |         6934 | Tuxedo Golf Course | 2025-06-30     |            27    |             0 |                0 |           100 |
| 2025-06-30     | Uber                  |             1    | purchase       |         6933 | Uber               | 2025-06-30     |             1    |             0 |                0 |           100 |
| 2025-06-30     | Faber Creative        |          1048.95 | purchase       |         6932 | Faber Creative     | 2025-06-30     |          1048.95 |             0 |                0 |           100 |
| 2025-06-30     | TD Bank               |           125    | purchase       |         6890 | TD Bank            | 2025-06-30     |           125    |             0 |                0 |           100 |

