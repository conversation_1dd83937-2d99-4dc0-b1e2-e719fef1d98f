SQL Query:

````
SELECT
  SUM(CASE 
    WHEN transaction_type = 'credit' AND (account_class IS NULL OR account_class = '') THEN -adjusted_amount
    WHEN account_class = 'Revenue' THEN adjusted_amount
    ELSE 0
  END) AS total_revenue
FROM quickbooks__general_ledger
WHERE transaction_date >= '2025-07-01' AND transaction_date < '2025-08-01';
````

Data retrieved successfully:

|   total_revenue |
|----------------:|
|          304475 |