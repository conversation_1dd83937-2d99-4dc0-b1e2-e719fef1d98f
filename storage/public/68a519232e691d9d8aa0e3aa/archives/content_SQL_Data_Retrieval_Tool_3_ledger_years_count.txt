SQL Query:

````
SELECT EXTRACT(YEAR FROM transaction_date) AS year, COUNT(*) AS row_count
FROM QuickBooks_Reports.quickbooks__general_ledger
GROUP BY year
ORDER BY year DESC;
````

Data retrieved successfully:

|   year |   row_count |
|-------:|------------:|
|   2025 |        3075 |
|   2024 |        3602 |
|   2023 |        3102 |
|   2022 |        3099 |
|   2021 |        2391 |
|   2020 |        2639 |
|   2019 |        2990 |
|   2018 |        3878 |
|   2017 |         688 |