SQL Query:

````
SELECT
  DATE_TRUNC(transaction_date, MONTH) AS month_start,
  SUM(
    CASE
      WHEN transaction_type = 'credit' AND (account_class IS NULL OR account_class = '') THEN -adjusted_amount
      WHEN account_class = 'Revenue' THEN adjusted_amount
      ELSE 0
    END
  ) AS total_revenue
FROM
  QuickBooks_Reports.quickbooks__general_ledger
WHERE
  transaction_date >= '2025-01-01'
  AND transaction_date <= '2025-12-31'
  AND transaction_date <= CURRENT_DATE()
  AND (
    (transaction_type = 'credit' AND (account_class IS NULL OR account_class = ''))
    OR account_class = 'Revenue'
  )
GROUP BY
  month_start
ORDER BY
  month_start ASC;
````

Data retrieved successfully:

| month_start             |   total_revenue |
|:------------------------|----------------:|
| {'value': '2025-01-01'} |          304278 |
| {'value': '2025-02-01'} |          241168 |
| {'value': '2025-03-01'} |          408970 |
| {'value': '2025-04-01'} |          253487 |
| {'value': '2025-05-01'} |          363397 |
| {'value': '2025-06-01'} |          231018 |
| {'value': '2025-07-01'} |          304475 |
| {'value': '2025-08-01'} |           33468 |