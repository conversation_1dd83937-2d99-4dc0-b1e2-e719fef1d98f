SQL Query:

````
WITH months_2025 AS (
  SELECT DATE_TRUNC(DATE_ADD('2025-01-01', INTERVAL n MONTH), MONTH) AS month_start
  FROM UNNEST(GENERATE_ARRAY(0, 11)) AS n
),
revenue_by_month AS (
  SELECT
    DATE_TRUNC(transaction_date, MONTH) AS month_start,
    SUM(
      CASE
        WHEN transaction_type = 'credit' AND (account_class IS NULL OR account_class = '') THEN -adjusted_amount
        WHEN account_class = 'Revenue' THEN adjusted_amount
        ELSE 0
      END
    ) AS total_revenue
  FROM
    quickbooks__general_ledger
  WHERE
    transaction_date BETWEEN '2025-01-01' AND '2025-12-31'
  GROUP BY
    month_start
)
SELECT
  m.month_start AS Month,
  IFNULL(r.total_revenue, 0) AS Total_Revenue
FROM
  months_2025 m
LEFT JOIN
  revenue_by_month r
ON
  m.month_start = r.month_start
ORDER BY
  m.month_start ASC;
````

Data retrieved successfully:

| Month                   |   Total_Revenue |
|:------------------------|----------------:|
| {'value': '2025-01-01'} |          304278 |
| {'value': '2025-02-01'} |          241168 |
| {'value': '2025-03-01'} |          408970 |
| {'value': '2025-04-01'} |          253487 |
| {'value': '2025-05-01'} |          363397 |
| {'value': '2025-06-01'} |          231018 |
| {'value': '2025-07-01'} |          304475 |
| {'value': '2025-08-01'} |           33468 |
| {'value': '2025-09-01'} |               0 |
| {'value': '2025-10-01'} |               0 |
| {'value': '2025-11-01'} |           -3375 |
| {'value': '2025-12-01'} |               0 |