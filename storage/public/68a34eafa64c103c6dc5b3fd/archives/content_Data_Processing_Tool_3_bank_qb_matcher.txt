Python code:

```python
import pandas as pd
import numpy as np
import os
from datetime import datetime
from rapidfuzz import fuzz
from metalake import load_data

chat_id = "68a34cfba64c103c6dc5b32b"

# 1. Load data
df_bank = load_data(chat_id, 'TD_bank_payments_all_data.dat')
df_qb = load_data(chat_id, 'bill_payment_lines_recon.dat')

if df_bank.empty or df_qb.empty:
    print("One or both input files are empty. Please check their contents.")
else:
    # ---- BANK DATA PREP ----
    # a. Keep columns and rename
    keep_cols = {'Date': 'bank_tx_date', 'Payee': 'Payee', 'DESCRIPTION': 'DESCRIPTION', 
                 'Memo/Description': 'Memo/Description', 'Total': 'bank_tx_amount'}
    found_cols = {col: col for col in df_bank.columns if col in keep_cols}
    # Only keep and rename if present
    dfb = df_bank[[col for col in keep_cols if col in df_bank.columns]].copy()
    dfb.rename(columns=keep_cols, inplace=True)
    # b. Description: first non-null of Payee, DESCRIPTION, Memo/Description
    dfb['bank_tx_description'] = dfb[['Payee','DESCRIPTION','Memo/Description']].bfill(axis=1).iloc[:,0]
    # c. Standardise amount and date
    def parse_amount(val):
        try:
            return abs(float(val))
        except:
            return np.nan
    dfb['bank_tx_amount'] = dfb['bank_tx_amount'].apply(parse_amount)
    def parse_date(val):
        if pd.isnull(val): return pd.NaT
        for fmt in ('%m/%d/%Y', '%Y-%m-%d', '%Y/%m/%d'):
            try:
                return pd.to_datetime(val, format=fmt)
            except:
                continue
        # fallback to pandas parse
        try:
            return pd.to_datetime(val)
        except:
            return pd.NaT
    dfb['bank_tx_date'] = dfb['bank_tx_date'].apply(parse_date)
    # Final keep: bank_tx_date, bank_tx_description, bank_tx_amount
    main_bank_cols = ['bank_tx_date','bank_tx_description','bank_tx_amount']
    # Keep for output:
    out_bank_cols = main_bank_cols + ['Payee', 'DESCRIPTION', 'Memo/Description']
    dfb = dfb[main_bank_cols + ['Payee','DESCRIPTION','Memo/Description']]
    # ---- QUICKBOOKS DATA PREP ----
    # Remove JSON wrapper from payment_date
    def unjson_payment_date(val):
        if pd.isnull(val): return None
        if isinstance(val, dict) and 'value' in val:
            return val['value']
        if isinstance(val, str) and val.startswith('{') and 'value' in val:
            import json
            try:
                d = json.loads(val.replace("'", '"'))
                return d.get('value')
            except:
                return val
        return val
    if 'payment_date' in df_qb.columns:
        df_qb['payment_date'] = df_qb['payment_date'].apply(unjson_payment_date)
        df_qb['payment_date'] = pd.to_datetime(df_qb['payment_date'], errors='coerce')
    # Standardise payment_total_amount
    def safe_abs(val):
        try:
            return abs(float(val))
        except:
            return np.nan
    if 'payment_total_amount' in df_qb.columns:
        df_qb['payment_total_amount'] = df_qb['payment_total_amount'].apply(safe_abs)
    # vendor_name: vendor_display_name or print_on_check_name
    df_qb['vendor_name'] = df_qb[['vendor_display_name','print_on_check_name']].bfill(axis=1).iloc[:,0]
    # Group by bill_payment_id
    agg_cols = ['payment_total_amount','vendor_name','payment_date','doc_number']
    qb_grouped = (df_qb.groupby('bill_payment_id')[agg_cols]
                  .first().reset_index())
    # Remove rows with missing vendor_name or payment_total_amount
    qb_grouped = qb_grouped[~qb_grouped['vendor_name'].isnull()]
    qb_grouped = qb_grouped[~qb_grouped['payment_total_amount'].isnull()]
    # ---- MATCHING ----
    results = []
    for idx, row in dfb.iterrows():
        desc = str(row['bank_tx_description']) if pd.notnull(row['bank_tx_description']) else ''
        amt = row['bank_tx_amount']
        date = row['bank_tx_date']
        # Initial candidate match: amount diff ≤1, fuzz ≥80
        candidates = qb_grouped[
            (qb_grouped['payment_total_amount'].sub(amt).abs() <= 1)
        ].copy()
        if not candidates.empty:
            candidates['match_score'] = candidates['vendor_name'].apply(
                lambda x: fuzz.token_sort_ratio(desc, str(x)) if pd.notnull(x) else 0)
            candidates = candidates[candidates['match_score'] >= 80]
        # If none, broaden tolerance
        if candidates.empty and not np.isnan(amt):
            # amount_tolerance as 2% of payment_total_amount
            qb_grouped['amount_diff'] = qb_grouped['payment_total_amount'].sub(amt).abs()
            qb_grouped['amount_tol'] = qb_grouped['payment_total_amount'] * 0.02
            candidates = qb_grouped[(qb_grouped['amount_diff'] <= qb_grouped['amount_tol'])].copy()
            if not candidates.empty:
                candidates['match_score'] = candidates['vendor_name'].apply(
                    lambda x: fuzz.token_sort_ratio(desc, str(x)) if pd.notnull(x) else 0)
                candidates = candidates[candidates['match_score'] >= 85]
        if not candidates.empty:
            # Compute date difference
            candidates['date_diff'] = (candidates['payment_date'] - date).abs().dt.days
            # Prefer date diff ≤7, then smallest
            mask_7 = candidates['date_diff'] <= 7
            if mask_7.any():
                candidates = candidates[mask_7]
            # Choose best by match_score, then smallest date_diff, then smallest amount_diff
            candidates = candidates.sort_values(['match_score','date_diff','amount_diff' if 'amount_diff' in candidates else 'payment_total_amount'], ascending=[False,True,True])
            best = candidates.iloc[0]
            results.append({
                'bank_tx_date': row['bank_tx_date'],
                'bank_tx_description': desc,
                'bank_tx_amount': amt,
                'bill_payment_id': best['bill_payment_id'],
                'vendor_name': best['vendor_name'],
                'payment_date': best['payment_date'],
                'bill_description': best['doc_number'],
                'additional_info': f"match_score={best['match_score']}, amount_diff={abs(best['payment_total_amount']-amt):.2f}, date_diff={best['date_diff']}"
            })
        else:
            # No match
            results.append({
                'bank_tx_date': row['bank_tx_date'],
                'bank_tx_description': desc,
                'bank_tx_amount': amt,
                'bill_payment_id': '',
                'vendor_name': '',
                'payment_date': '',
                'bill_description': '',
                'additional_info': 'No match'
            })
    matched_df = pd.DataFrame(results)
    # Format monetary and date fields
    matched_df['bank_tx_amount'] = matched_df['bank_tx_amount'].map(lambda x: f"{x:.2f}" if pd.notnull(x) else "")
    matched_df['bank_tx_date'] = matched_df['bank_tx_date'].dt.strftime('%Y-%m-%d')
    matched_df['payment_date'] = pd.to_datetime(matched_df['payment_date'], errors='coerce').dt.strftime('%Y-%m-%d')
    # Save result
    os.makedirs('files', exist_ok=True)
    matched_df.to_csv('files/matched_results.csv', index=False)
    matched_df.to_csv('files/matched_results.dat', index=False)
    # Markdown preview
    preview = matched_df.head(15)
    print(preview.to_markdown(index=False))
    if len(matched_df) > 15:
        print(f"\n(Only first 15 of {len(matched_df)} rows shown above.)")
````

Output:



