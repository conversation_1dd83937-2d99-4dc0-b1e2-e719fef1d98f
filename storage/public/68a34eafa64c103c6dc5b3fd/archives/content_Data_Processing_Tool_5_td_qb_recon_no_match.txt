Python code:

```python
import pandas as pd
import numpy as np
from datetime import datetime
import difflib
import os
from metalake import load_data

def first_nonnull(*args):
    for a in args:
        if pd.notnull(a) and str(a).strip():
            return str(a).strip()
    return ''

def fuzzy_similarity(a, b):
    a = str(a or '').lower().strip()
    b = str(b or '').lower().strip()
    if not a or not b:
        return 0.0
    return difflib.SequenceMatcher(None, a, b).ratio()

def parse_bank_date(s):
    if pd.isnull(s):
        return pd.NaT
    try:
        return pd.to_datetime(s, format="%m/%d/%Y", errors='coerce')
    except Exception:
        return pd.to_datetime(s, errors='coerce')

def parse_qb_date(val):
    if pd.isnull(val):
        return pd.NaT
    if isinstance(val, dict):
        v = val.get('value', None)
    else:
        v = str(val)
        if v.startswith("{'value': "):
            v = v.replace("{'value': '", '').replace("'}", '')
    try:
        return pd.to_datetime(v, format="%Y-%m-%d", errors='coerce')
    except Exception:
        return pd.to_datetime(v, errors='coerce')

def parse_amount(val):
    if pd.isnull(val):
        return np.nan
    try:
        return abs(float(val))
    except Exception:
        try:
            return abs(float(str(val).replace(',', '').replace('$', '')))
        except:
            return np.nan

chat_id = "68a34e3ba64c103c6dc5b3cb"
bank_file = 'TD_bank_payments_all_data.dat'
qb_file = 'bill_payment_lines_recon.dat'

bank_df = load_data(chat_id, bank_file)
qb_df = load_data(chat_id, qb_file)

if bank_df.empty or qb_df.empty:
    print("One or both input files are empty. Please check the data sources.")
else:
    # --- BANK DATA CLEAN ---
    col_map = {
        'Date': 'bank_tx_date',
        'Payee': 'Payee',
        'DESCRIPTION': 'DESCRIPTION',
        'Memo/Description': 'Memo/Description',
        'Total': 'bank_tx_amount'
    }
    cols_to_use = {}
    for k, v in col_map.items():
        if k in bank_df.columns:
            cols_to_use[k] = v
    if 'Total' not in bank_df.columns and 'AMOUNT' in bank_df.columns:
        cols_to_use['AMOUNT'] = 'bank_tx_amount'
    bank_working = bank_df[list(cols_to_use.keys())].copy()
    bank_working.rename(columns=cols_to_use, inplace=True)
    for col in ['Payee', 'DESCRIPTION', 'Memo/Description']:
        if col not in bank_working.columns:
            bank_working[col] = None
    bank_working['bank_tx_description'] = bank_working.apply(
        lambda row: first_nonnull(row.get('Payee'), row.get('DESCRIPTION'), row.get('Memo/Description')),
        axis=1
    )
    bank_working['bank_tx_date'] = bank_working['bank_tx_date'].apply(parse_bank_date)
    bank_working['bank_tx_amount'] = bank_working['bank_tx_amount'].apply(parse_amount)

    # --- QUICKBOOKS DATA CLEAN ---
    qb_df['payment_date'] = qb_df['payment_date'].apply(parse_qb_date)
    qb_agg = qb_df.groupby('bill_payment_id', as_index=False).agg({
        'payment_date': 'first',
        'payment_total_amount': 'first',
        'vendor_display_name': 'first',
        'print_on_check_name': 'first',
        'doc_number': 'first'
    })
    qb_agg['payment_total_amount'] = qb_agg['payment_total_amount'].apply(parse_amount)
    qb_agg['vendor_name'] = qb_agg.apply(
        lambda row: first_nonnull(row.get('vendor_display_name'), row.get('print_on_check_name')),
        axis=1
    )

    # --- MATCHING CASCADE ---
    results = []
    for idx, bank_row in bank_working.iterrows():
        bdate = bank_row['bank_tx_date']
        bdesc = bank_row['bank_tx_description']
        bamt = bank_row['bank_tx_amount']
        if pd.isnull(bamt) or pd.isnull(bdate):
            results.append({
                'bank_tx_date': bdate,
                'bank_tx_description': bdesc,
                'bank_tx_amount': bamt,
                'bill_payment_id': '',
                'vendor_name': '',
                'payment_date': '',
                'bill_description': '',
                'match_level': 0,
                'additional_info': 'Blank bank row'
            })
            continue
        candidates = qb_agg[qb_agg['payment_total_amount'].notnull()].copy()
        candidates['amount_diff'] = (candidates['payment_total_amount'] - bamt).abs()
        candidates['date_diff'] = candidates['payment_date'].apply(
            lambda x: abs((bdate - x).days) if pd.notnull(x) and pd.notnull(bdate) else np.inf)
        candidates['fuzzy_score'] = candidates['vendor_name'].apply(lambda x: fuzzy_similarity(bdesc, x))
        match = None
        level = 0
        info = ''
        # Level 1
        rule1 = (candidates['amount_diff'] <= 1.00) & (candidates['fuzzy_score'] >= 0.80)
        c1 = candidates[rule1]
        if len(c1) == 1:
            match = c1.iloc[0]
            level = 1
            info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}, fuzzy={match['fuzzy_score']:.2f}"
        elif len(c1) > 1:
            min_date = c1['date_diff'].min()
            c1 = c1[c1['date_diff'] == min_date]
            if len(c1) > 1:
                c1 = c1.sort_values('payment_date')
            match = c1.iloc[0]
            level = 1
            info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}, fuzzy={match['fuzzy_score']:.2f}"
        else:
            # Level 2
            rule2 = (candidates['amount_diff'] <= 1.00) & (candidates['date_diff'] <= 3)
            c2 = candidates[rule2]
            if len(c2) == 1:
                match = c2.iloc[0]
                level = 2
                info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}"
            elif len(c2) > 1:
                min_date = c2['date_diff'].min()
                c2 = c2[c2['date_diff'] == min_date]
                if len(c2) > 1:
                    c2 = c2.sort_values('payment_date')
                match = c2.iloc[0]
                level = 2
                info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}"
            else:
                # Level 3
                percent = candidates['payment_total_amount'] * 0.005
                rule3 = (candidates['amount_diff'] <= percent) & (candidates['date_diff'] <= 3)
                c3 = candidates[rule3]
                if len(c3) == 1:
                    match = c3.iloc[0]
                    level = 3
                    info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}"
                elif len(c3) > 1:
                    min_date = c3['date_diff'].min()
                    c3 = c3[c3['date_diff'] == min_date]
                    if len(c3) > 1:
                        c3 = c3.sort_values('payment_date')
                    match = c3.iloc[0]
                    level = 3
                    info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}"
                else:
                    # Level 4
                    rule4 = (candidates['amount_diff'] <= 2.00) & (candidates['date_diff'] <= 7)
                    c4 = candidates[rule4]
                    if len(c4) == 1:
                        match = c4.iloc[0]
                        level = 4
                        info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}"
                    elif len(c4) > 1:
                        min_date = c4['date_diff'].min()
                        c4 = c4[c4['date_diff'] == min_date]
                        if len(c4) > 1:
                            c4 = c4.sort_values('payment_date')
                        match = c4.iloc[0]
                        level = 4
                        info = f"amt_diff={match['amount_diff']:.2f}, date_diff={match['date_diff']}"
        if match is not None:
            results.append({
                'bank_tx_date': bdate,
                'bank_tx_description': bdesc,
                'bank_tx_amount': bamt,
                'bill_payment_id': match.get('bill_payment_id', ''),
                'vendor_name': match.get('vendor_name', ''),
                'payment_date': match.get('payment_date', ''),
                'bill_description': match.get('doc_number', ''),
                'match_level': level,
                'additional_info': info
            })
        else:
            results.append({
                'bank_tx_date': bdate,
                'bank_tx_description': bdesc,
                'bank_tx_amount': bamt,
                'bill_payment_id': '',
                'vendor_name': '',
                'payment_date': '',
                'bill_description': '',
                'match_level': 0,
                'additional_info': 'No match'
            })
    result_df = pd.DataFrame(results, columns=[
        'bank_tx_date', 'bank_tx_description', 'bank_tx_amount',
        'bill_payment_id', 'vendor_name', 'payment_date', 'bill_description',
        'match_level', 'additional_info'
    ])
    os.makedirs('files', exist_ok=True)
    out_csv = 'files/matched_results_v2.csv'
    out_dat = 'files/matched_results_v2.dat'
    result_df.to_csv(out_csv, index=False)
    result_df.to_csv(out_dat, index=False)
    # Display markdown preview (first 15 rows)
    preview = result_df.head(15)
    print(preview.to_markdown(index=False))
    if len(result_df) > 15:
        print(f"\n(Only first 15 of {len(result_df)} rows shown above.)")

````

Output:

| bank_tx_date        | bank_tx_description           |   bank_tx_amount | bill_payment_id   | vendor_name   | payment_date   | bill_description   |   match_level | additional_info   |
|:--------------------|:------------------------------|-----------------:|:------------------|:--------------|:---------------|:-------------------|--------------:|:------------------|
| 2025-06-30 00:00:00 | Tuxedo Golf Course            |            28.8  |                   |               |                |                    |             0 | No match          |
| 2025-06-30 00:00:00 | Tuxedo Golf Course            |            27    |                   |               |                |                    |             0 | No match          |
| 2025-06-30 00:00:00 | Uber                          |             1    |                   |               |                |                    |             0 | No match          |
| 2025-06-30 00:00:00 | Faber Creative                |          1048.95 |                   |               |                |                    |             0 | No match          |
| 2025-06-30 00:00:00 | TD Bank                       |           125    |                   |               |                |                    |             0 | No match          |
| 2025-06-30 00:00:00 | TD Bank                       |            15    |                   |               |                |                    |             0 | No match          |
| 2025-06-29 00:00:00 | Joey Restaurant               |           165.32 |                   |               |                |                    |             0 | No match          |
| 2025-06-27 00:00:00 | 4 Seasons Heating & Cooling   |           168    |                   |               |                |                    |             0 | No match          |
| 2025-06-27 00:00:00 | Better Proposals              |           137.98 |                   |               |                |                    |             0 | No match          |
| 2025-06-27 00:00:00 | Lourdes Azpillaga             |          3503    |                   |               |                |                    |             0 | No match          |
| 2025-06-27 00:00:00 | Joanie Cassidy                |          2098.23 |                   |               |                |                    |             0 | No match          |
| 2025-06-27 00:00:00 | Mario Ricardo Velasquez Ramos |          2035.54 |                   |               |                |                    |             0 | No match          |
| 2025-06-27 00:00:00 | Yarenis Fernandez             |          3782.54 |                   |               |                |                    |             0 | No match          |
| 2025-06-26 00:00:00 | The Keg                       |           149.43 |                   |               |                |                    |             0 | No match          |
| 2025-06-26 00:00:00 | Bistro Inferno                |           120.96 |                   |               |                |                    |             0 | No match          |

(Only first 15 of 714 rows shown above.)

