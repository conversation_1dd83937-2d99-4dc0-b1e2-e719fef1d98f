Metadata of all data sources:

````
[
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "invoice": {
        "table_name": "invoice",
        "fields": {
          "id": {
            "name": "id",
            "description": "Primary key uniquely identifying each invoice record in the system.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "71",
              "77",
              "128",
              "130",
              "132",
              "and 1682 more..."
            ]
          },
          "balance": {
            "name": "balance",
            "description": "The remaining unpaid amount on the invoice, reflecting what the customer still owes.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "5733",
              "8400",
              "8820",
              "10500",
              "and 63 more..."
            ]
          },
          "global_tax_calculation": {
            "name": "global_tax_calculation",
            "description": "Defines the tax application method for the invoice, such as excluding or not applying tax to line items.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "TaxExcluded",
              "NotApplicable"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp capturing when the invoice record was initially created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-30 19:28:22+00:00",
              "2018-03-30 19:23:24+00:00",
              "2018-03-30 19:26:21+00:00",
              "2018-03-30 20:08:44+00:00",
              "2018-03-30 19:25:22+00:00",
              "and 1682 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp of the latest modification made to the invoice record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-04-16 16:01:16+00:00",
              "2024-09-16 15:51:18+00:00",
              "2024-05-24 12:03:19+00:00",
              "2023-10-23 13:36:47+00:00",
              "2021-05-14 13:53:40+00:00",
              "and 1507 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The grand total billed on the invoice, including all line items and taxes, representing the full amount due.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1050",
              "2625",
              "3024",
              "4725",
              "6300",
              "and 724 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official accounting date assigned to the invoice, used for reporting and ledger entries.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-10-31",
              "2019-01-02",
              "2022-05-22",
              "2022-07-10",
              "2021-11-01",
              "and 867 more..."
            ]
          },
          "sales_term_id": {
            "name": "sales_term_id",
            "description": "Foreign key linking to the payment terms (e.g., Net 30) in the term table, which dictates when payment is due.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "4",
              "6"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Foreign key linking to the customer associated with this invoice, identifying the billed party.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "7",
              "9",
              "10",
              "11",
              "14",
              "and 370 more..."
            ]
          }
        }
      },
      "invoice_line": {
        "table_name": "invoice_line",
        "fields": {
          "invoice_id": {
            "name": "invoice_id",
            "description": "Foreign key referencing the parent invoice for this line item, uniquely identifying the invoice to which the line belongs.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "206",
              "208",
              "1621",
              "2200",
              "2648",
              "and 1682 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Zero-based position indicating the order of this line item within its invoice, ensuring uniqueness in combination with 'invoice_id'.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "description": {
            "name": "description",
            "description": "Free-text details or notes specific to this invoice line, such as product or service descriptions; may be blank or repeated across lines.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Permanent Placement",
              "placement of Marcia Baptista",
              "Yulia Sorokotiaha (80 hours)",
              "Rent & Admin",
              "placement of Nimali Perera",
              "and 1634 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Total monetary value for this invoice line, representing the extended or subtotal amount depending on line type.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "2500",
              "2880",
              "3123",
              "4500",
              "6000",
              "and 731 more..."
            ]
          },
          "detail_type": {
            "name": "detail_type",
            "description": "Categorizes the nature of the line item, distinguishing among group lines, subtotals, and individual sales items to determine relevant fields.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "SubTotalLineDetail",
              "SalesItemLineDetail",
              "GroupLineDetail"
            ]
          },
          "sales_item_unit_price": {
            "name": "sales_item_unit_price",
            "description": "Price charged per unit for the sales item on this line, used when the line represents a sales item detail.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "36",
              "100",
              "130",
              "2500",
              "3123",
              "and 598 more..."
            ]
          },
          "sales_item_item_id": {
            "name": "sales_item_item_id",
            "description": "Foreign key referencing the specific item or product sold on this line, linking to the 'item' table when applicable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7"
            ]
          },
          "sales_item_quantity": {
            "name": "sales_item_quantity",
            "description": "Number of units or quantity of service provided on this line, relevant only for sales item detail lines.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "10",
              "12",
              "20",
              "30",
              "and 87 more..."
            ]
          },
          "sales_item_account_id": {
            "name": "sales_item_account_id",
            "description": "Foreign key linking this sales item line to its related account in the 'account' table for financial categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "10",
              "31",
              "52",
              "75",
              "129",
              "137",
              "141"
            ]
          },
          "sales_item_tax_code_id": {
            "name": "sales_item_tax_code_id",
            "description": "Foreign key indicating the tax code applied to this sales item line, determining tax calculation and treatment.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5"
            ]
          }
        }
      },
      "deposit": {
        "table_name": "deposit",
        "fields": {
          "id": {
            "name": "id",
            "description": "Primary key; uniquely identifies each deposit transaction record in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "6",
              "7",
              "1827",
              "2187",
              "and 28 more..."
            ]
          },
          "global_tax_calculation": {
            "name": "global_tax_calculation",
            "description": "Specifies how tax is handled for the entire deposit, indicating whether tax is included or not applicable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "NotApplicable",
              "TaxInclusive"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp marking when the deposit record was first created in the system for audit and historical purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-05-17 15:53:44+00:00",
              "2020-08-07 17:45:11+00:00",
              "2020-08-07 20:37:57+00:00",
              "2020-08-07 20:42:17+00:00",
              "2020-10-19 18:12:21+00:00",
              "and 28 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp showing the most recent modification to the deposit record, tracking change history.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-05-17 15:53:44+00:00",
              "2020-08-07 17:45:11+00:00",
              "2020-08-07 20:37:57+00:00",
              "2020-08-07 20:42:17+00:00",
              "2020-10-19 18:12:21+00:00",
              "and 28 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Total monetary value of the deposit transaction, stored with high precision for financial accuracy.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "125",
              "300",
              "21.37",
              "0.95",
              "and 26 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "Calendar date when the deposit transaction officially took place, used for accounting and reconciliation.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-10-31",
              "2025-05-30",
              "2020-03-31",
              "2021-01-29",
              "2020-08-31",
              "and 25 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Foreign key referencing the destination account that received the deposit, linking to account details.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "53",
              "54",
              "55",
              "89",
              "90"
            ]
          }
        }
      },
      "deposit_line": {
        "table_name": "deposit_line",
        "fields": {
          "deposit_id": {
            "name": "deposit_id",
            "description": "Foreign key referencing the 'deposit' table, uniquely identifying the parent deposit for this line item and grouping multiple lines under a single deposit transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "6",
              "7",
              "1827",
              "2187",
              "and 28 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Sequential number representing this line item's position within its parent deposit, ensuring uniqueness together with deposit_id.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1"
            ]
          },
          "description": {
            "name": "description",
            "description": "Optional text field providing specific details or context about the purpose or nature of this individual deposit line item.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "ACCT BAL REBATE",
              "Deposit in Error - paid x2",
              "Mani Reddy RECLAIM E-TFR",
              "Adj",
              "RECLAIM E-TFR ***Gsf View more RECLAIM E-TFR ***Gsf"
            ]
          },
          "detail_type": {
            "name": "detail_type",
            "description": "Constant string field with the value 'DepositLineDetail', denoting the record type for compatibility with systems that support multiple line detail types.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "DepositLineDetail"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value attributed to this deposit line item, indicating the credited or debited amount for this portion of the deposit.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "125",
              "300",
              "21.37",
              "23.91",
              "and 27 more..."
            ]
          },
          "deposit_tax_code_id": {
            "name": "deposit_tax_code_id",
            "description": "Foreign key to the 'tax_code' table, identifying the specific tax code applied to this deposit line when tax is relevant.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2"
            ]
          },
          "deposit_account_id": {
            "name": "deposit_account_id",
            "description": "Foreign key to the 'account' table, identifying the financial account to which this deposit line is posted.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "19",
              "46",
              "52",
              "67",
              "102",
              "141"
            ]
          },
          "deposit_customer_id": {
            "name": "deposit_customer_id",
            "description": "Foreign key to the 'customer' table, optionally linking this deposit line to a specific customer involved in the transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "71",
              "555",
              "614",
              "621"
            ]
          }
        }
      },
      "customer": {
        "table_name": "customer",
        "fields": {
          "id": {
            "name": "id",
            "description": "A system-generated unique string serving as the primary key for each customer, used to identify and reference individual customer records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "8",
              "9",
              "10",
              "11",
              "and 380 more..."
            ]
          },
          "company_name": {
            "name": "company_name",
            "description": "The formal business or organization name linked to the customer; blank for individuals and nearly unique for companies.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "G3 Canada Ltd",
              "OnBusiness Chartered Professional Accountants",
              "Winnipeg School Division",
              "ARTIS Reit",
              "COFCO International",
              "and 336 more..."
            ]
          },
          "display_name": {
            "name": "display_name",
            "description": "The unique, preferred name shown for the customer in user interfaces and reports, which may be a company or person.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "The Boyd Group",
              "OnBusiness Chartered Professional Accountants",
              "Winnipeg School Division",
              "ARTIS Reit",
              "COFCO International",
              "and 380 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "A boolean indicating whether the customer is currently active (true) or inactive (false) in the system.",
            "dataType": "BOOL",
            "is_unstructured": false
          }
        }
      },
      "account": {
        "table_name": "account",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated unique identifier for each account record, serving as the primary key within the chart of accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "12",
              "13",
              "14",
              "15",
              "and 189 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Categorical label denoting the functional group of the account (e.g., 'Accounts Payable'), used for operational grouping and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Current Asset",
              "Other Expense",
              "Other Current Liability",
              "Cost of Goods Sold",
              "Accounts Receivable",
              "Fixed Asset",
              "Credit Card",
              "Bank",
              "Accounts Payable",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "name": {
            "name": "name",
            "description": "User-facing display name for the account, nearly unique and designed for selection in application interfaces.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5785",
              "Insurance",
              "Corporate Tax Receivable",
              "Allowance For Doubtful Accounts",
              "Accounts Receivable",
              "and 187 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "Boolean indicator showing if the account is active (true) or inactive/deleted (false) for filtering and soft deletion.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "classification": {
            "name": "classification",
            "description": "High-level financial category (such as 'Asset', 'Liability', 'Expense', etc.) that determines the account's grouping on financial statements.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Asset",
              "Liability",
              "Revenue",
              "Equity"
            ]
          }
        }
      },
      "item": {
        "table_name": "item",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string-based primary key that identifies each product or service entry in the item table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9"
            ]
          },
          "name": {
            "name": "name",
            "description": "A concise, human-friendly label for the item, typically used in user interfaces and reports, and unique within the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Opening Balance",
              "Perm",
              "Bank Charge",
              "Office Expenses",
              "Bad Debt",
              "Office Rental",
              "Contract",
              "Temp",
              "Legal"
            ]
          },
          "type": {
            "name": "type",
            "description": "An enumerated string specifying the item's classification, indicating whether it is a 'Service' or a 'NonInventory' product for accounting purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Service",
              "NonInventory"
            ]
          },
          "income_account_id": {
            "name": "income_account_id",
            "description": "Foreign key referencing the income account in the account table that revenue from this item is attributed to.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "18",
              "19",
              "31",
              "34",
              "52",
              "75",
              "129",
              "137",
              "141"
            ]
          }
        }
      },
      "class": {
        "table_name": "class",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string identifier that serves as the primary key for each business division (class) record in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "name": {
            "name": "name",
            "description": "A concise, human-readable name for the business division, uniquely identifying the class in user interfaces or selection lists.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "People Culture",
              "Executive",
              "Accounting Operations",
              "Finance",
              "Global Advisory",
              "Information Technology"
            ]
          }
        }
      }
    },
    "reasoning": "  \n  \n**Reasoning:**  \n  \nTo compute total revenue for July 2025, focus on sources of recognized revenue: invoices, invoice lines, and deposits (with deposit lines). Required columns include transaction dates, revenue/amount fields, and status or document state. Joins to customer, account, and item are needed for context, and all necessary foreign/primary keys are included. Only valid columns per metadata are selected.  \n  \n  \n**Table Columns:**   \n- table: invoice  \n  columns: id, transaction_date, total_amount, balance, global_tax_calculation, created_at, updated_at, customer_id, sales_term_id  \n  \n- table: invoice_line  \n  columns: invoice_id, amount, sales_item_item_id, description, detail_type, sales_item_unit_price, sales_item_quantity, index, sales_item_account_id, sales_item_tax_code_id  \n  \n- table: deposit  \n  columns: id, transaction_date, total_amount, created_at, updated_at, account_id, global_tax_calculation  \n  \n- table: deposit_line  \n  columns: deposit_id, amount, description, detail_type, deposit_account_id, deposit_tax_code_id, deposit_customer_id, index  \n  \n- table: customer  \n  columns: id, display_name, company_name, active  \n  \n- table: account  \n  columns: id, name, account_type, classification, active  \n  \n- table: item  \n  columns: id, name, type, income_account_id  \n  \n- table: class  \n  columns: id, name  \n  \n  \n---",
    "other_table_columns": {
      "item": [
        "fully_qualified_name",
        "sync_token",
        "sales_tax_included",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [
        "invoice_id",
        "index",
        "line_num",
        "description",
        "amount",
        "unit_price",
        "item_id",
        "class_id",
        "quantity",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [
        "id",
        "sync_token",
        "balance",
        "doc_number",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "due_date",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "estimate_line": [],
      "journal_entry_tax_line": [
        "journal_entry_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "term": [
        "id",
        "name",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [
        "purchase_id",
        "index",
        "amount",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "credit_card_payment_txn": [
        "id",
        "amount",
        "currency_id",
        "created_at",
        "updated_at",
        "transaction_date",
        "bank_account_id",
        "credit_card_account_id",
        "_fivetran_synced"
      ],
      "tax_code": [
        "id",
        "active",
        "description",
        "name",
        "taxable",
        "tax_group",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_type_applicable",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [
        "bill_id",
        "index",
        "bill_payment_id",
        "_fivetran_synced"
      ],
      "transfer": [
        "id",
        "amount",
        "private_note",
        "created_at",
        "updated_at",
        "currency_id",
        "transaction_date",
        "to_account_id",
        "from_account_id",
        "_fivetran_synced"
      ],
      "vendor": [
        "id",
        "family_name",
        "given_name",
        "company_name",
        "active",
        "balance",
        "display_name",
        "print_on_check_name",
        "middle_name",
        "sync_token",
        "billing_address_id",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [
        "invoice_id",
        "index",
        "payment_id",
        "_fivetran_synced"
      ],
      "deposit_line": [
        "deposit_tax_applicable_on",
        "deposit_class_id",
        "_fivetran_synced"
      ],
      "tax_agency": [
        "id",
        "display_name",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [
        "id",
        "pay_type",
        "doc_number",
        "sync_token",
        "currency_id",
        "check_print_status",
        "check_bank_account_id",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "account": [
        "currency_id",
        "description",
        "fully_qualified_name",
        "sub_account",
        "account_sub_type",
        "sync_token",
        "created_at",
        "updated_at",
        "balance_with_sub_accounts",
        "balance",
        "account_number",
        "parent_account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "address": [
        "id",
        "line_1",
        "line_2",
        "line_3",
        "line_4",
        "city",
        "postal_code",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [
        "id",
        "email_status",
        "doc_number",
        "sync_token",
        "global_tax_calculation",
        "shipping_address_id",
        "billing_address_id",
        "bill_email",
        "customer_memo",
        "total_tax",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "class": [
        "sync_token",
        "fully_qualified_name",
        "active",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bill_payment_line": [
        "bill_payment_id",
        "index",
        "amount",
        "bill_id",
        "journal_entry_id",
        "_fivetran_synced"
      ],
      "payment_line": [
        "payment_id",
        "index",
        "amount",
        "journal_entry_id",
        "credit_memo_id",
        "invoice_id",
        "_fivetran_synced"
      ],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "journal_entry": [
        "id",
        "doc_number",
        "sync_token",
        "private_note",
        "created_at",
        "updated_at",
        "transaction_date",
        "_fivetran_synced"
      ],
      "bill_line": [
        "bill_id",
        "index",
        "description",
        "amount",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "_fivetran_synced"
      ],
      "payment": [
        "id",
        "sync_token",
        "created_at",
        "updated_at",
        "reference_number",
        "unapplied_amount",
        "total_amount",
        "transaction_date",
        "payment_method_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "email_status",
        "doc_number",
        "print_status",
        "sync_token",
        "delivery_time",
        "billing_email",
        "customer_memo",
        "total_tax",
        "shipping_address_id",
        "billing_address_id",
        "due_date",
        "_fivetran_synced"
      ],
      "purchase": [
        "id",
        "sync_token",
        "credit",
        "doc_number",
        "payment_type",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "customer_id",
        "vendor_id",
        "remit_to_address_id",
        "total_tax",
        "total_amount",
        "transaction_date",
        "account_id",
        "payment_method_id",
        "_fivetran_synced"
      ],
      "customer": [
        "family_name",
        "fully_qualified_name",
        "given_name",
        "print_on_check_name",
        "title",
        "middle_name",
        "sync_token",
        "balance_with_jobs",
        "balance",
        "taxable",
        "created_at",
        "updated_at",
        "email",
        "website",
        "phone_number",
        "mobile_number",
        "shipping_address_id",
        "bill_address_id",
        "default_tax_code_id",
        "sales_term_id",
        "_fivetran_synced"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "id",
        "line_num",
        "sales_item_class_id",
        "bundle_quantity",
        "_fivetran_synced"
      ],
      "credit_memo_line": [
        "credit_memo_id",
        "index",
        "description",
        "amount",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_account_id",
        "_fivetran_synced"
      ],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "journal_entry_line": [
        "journal_entry_id",
        "index",
        "description",
        "amount",
        "posting_type",
        "customer_id",
        "vendor_id",
        "tax_applicable_on",
        "tax_amount",
        "class_id",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "payment_method": [
        "id",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [
        "purchase_id",
        "index",
        "amount",
        "description",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "account_expense_customer_id",
        "_fivetran_synced"
      ],
      "deposit": [
        "sync_token",
        "private_note",
        "_fivetran_synced"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Reports",
    "selected_table_column_metadata": {
      "quickbooks__expenses_sales_enhanced": {
        "table_name": "quickbooks__expenses_sales_enhanced",
        "fields": {
          "transaction_source": {
            "name": "transaction_source",
            "description": "Categorizes each transaction line as either an expense or a sale, enabling high-level separation for reporting and analytics.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "expense",
              "sales"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "System-generated identifier that groups all line items belonging to a single transaction; may repeat across multiple rows for multi-line transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "1158",
              "4723",
              "4918",
              "5445",
              "and 3938 more..."
            ]
          },
          "transaction_line_id": {
            "name": "transaction_line_id",
            "description": "Unique line item number within a specific transaction, used with 'transaction_id' to pinpoint individual transaction lines.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 37 more..."
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Enumerated value specifying the detailed classification of the transaction, such as 'invoice', 'bill', or 'journal_entry'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "journal_entry",
              "invoice",
              "bill",
              "purchase",
              "credit_memo",
              "deposit",
              "vendor_credit"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official date on which the transaction was recorded or occurred, used for period-based financial tracking.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2025-03-01",
              "2025-02-01",
              "2025-07-19",
              "and 1544 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Unique identifier for the general ledger (GL) account linked to this line, used for accounting and reporting purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "33",
              "and 67 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "Descriptive name of the GL account associated with the transaction line, aiding in categorization of financial activity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Perm Revenue",
              "Office Expenses",
              "Temp Revenue",
              "Meals and entertainment",
              "Wages & Salaries",
              "and 65 more..."
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Detailed sub-category of the GL account, offering finer granularity for accounting and expense tracking.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "OfficeGeneralAdministrativeExpenses",
              "ServiceFeeIncome",
              "PayrollExpenses",
              "EntertainmentMeals",
              "CostOfLabor",
              "BankCharges",
              "OtherMiscellaneousServiceCost",
              "AdvertisingPromotional",
              "OtherMiscellaneousExpense",
              "Travel",
              "Auto",
              "LegalProfessionalFees",
              "TaxesPaid",
              "Insurance",
              "TravelMeals",
              "InterestPaid",
              "BadDebts",
              "Amortization"
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "Optional code representing the business segment, department, or class associated with this transaction line for internal tracking.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value of the line item in the transaction's original currency, reflecting the specific entry before conversion.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Monetary value of the line item converted to a standard reporting currency, supporting consolidated financial analysis.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          }
        }
      },
      "quickbooks__profit_and_loss": {
        "table_name": "quickbooks__profit_and_loss",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The exact date associated with this financial record, reflecting when the transaction or summary was recorded in the accounting period.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-01",
              "2018-03-01",
              "2018-04-01",
              "2018-05-01",
              "2018-07-01",
              "and 96 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the accounting period this record summarizes, marking the beginning of the reporting interval (e.g., month or quarter).",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-01",
              "2018-03-01",
              "2018-04-01",
              "2018-05-01",
              "2018-07-01",
              "and 96 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The ending date of the accounting period this record summarizes, marking the close of the reporting interval.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-31",
              "2018-03-31",
              "2018-04-30",
              "2018-05-31",
              "2018-07-31",
              "and 96 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "Indicates whether the transaction relates to revenue or expense, providing a primary split for profit and loss categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Revenue"
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "A business-defined string identifier for categorizing the transaction or account, used for internal segmentation or grouping; may be absent for some records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Specifies the high-level financial category of the account, such as 'Expense', 'Income', 'Other Expense', or 'Other Income', aligning with standard financial statement classifications.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Expense",
              "Other Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides detailed categorization within the account type, describing the specific nature or purpose of the account (e.g., 'OfficeGeneralAdministrativeExpenses').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "OfficeGeneralAdministrativeExpenses",
              "PayrollExpenses",
              "OtherMiscellaneousServiceCost",
              "AdvertisingPromotional",
              "OtherMiscellaneousExpense",
              "ServiceFeeIncome",
              "CostOfLabor",
              "Auto",
              "EntertainmentMeals",
              "BankCharges",
              "OtherPrimaryIncome",
              "Travel",
              "LegalProfessionalFees",
              "BadDebts",
              "UnappliedCashPaymentIncome",
              "Amortization",
              "TaxesPaid",
              "TravelMeals",
              "InterestPaid",
              "InterestEarned",
              "DiscountsRefundsGiven",
              "Insurance"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A system-generated unique identifier for the account, ensuring precise account reference within the accounting software.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "32",
              "and 74 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The human-readable name of the account, such as 'Marketing' or 'Office Space Income', used for clear identification in reports.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Wages & Salaries",
              "EI Expense",
              "Employee Benefits",
              "Temp Payroll",
              "CPP Expense",
              "and 72 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The original transaction amount recorded for this entry, which may be positive or negative depending on the financial activity.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "27",
              "100",
              "150",
              "and 2135 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "The transaction amount expressed in a standardized or reporting currency, enabling consolidated analysis across multi-currency records.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "27",
              "100",
              "150",
              "and 2135 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \nTo compute total revenue for July 2025, we need transaction-level revenue details, dates, and status/type information. The key table is `quickbooks__expenses_sales_enhanced` (with revenue at the transaction line level) and `quickbooks__profit_and_loss` (periodic revenue summary). Status information is derived from `transaction_type` and possibly `transaction_source`. Primary/foreign key columns such as `transaction_id` and `class_id` are included for joins or further breakdowns.  \n  \n  \n**Table Columns:**  \n  \n- table: quickbooks__expenses_sales_enhanced  \n  columns: transaction_id, transaction_line_id, transaction_source, transaction_type, transaction_date, amount, converted_amount, account_id, account_name, account_sub_type, class_id  \n  \n- table: quickbooks__profit_and_loss  \n  columns: calendar_date, period_first_day, period_last_day, account_class, account_type, account_sub_type, account_id, account_name, amount, converted_amount, class_id  \n  \n  \n**Business Rules:**  \n- Revenue lines have `transaction_source = 'sales'` or `account_class = 'Revenue'` (profit_and_loss).  \n- For July 2025: `transaction_date` (or `calendar_date`, `period_first_day`, `period_last_day`) between '2025-07-01' and '2025-07-31'.  \n- Only completed/recognized transactions: reference `transaction_type` and exclude drafts/voids if such statuses exist (from context, likely only finalized transactions are present).  \n  \n  \n**ER Relationships:**  \n- `quickbooks__expenses_sales_enhanced.transaction_id` can join to other transaction tables if more detail is needed.  \n- `account_id` and `class_id` may join to account/class reference tables for enrichment, though not strictly needed for total revenue computation.  \n  ",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "doc_number",
        "item_id",
        "item_quantity",
        "item_unit_price",
        "customer_id",
        "customer_name",
        "customer_website",
        "vendor_id",
        "vendor_name",
        "billable_status",
        "description",
        "total_amount",
        "total_converted_amount"
      ],
      "quickbooks__cash_flow_statement": [
        "cash_flow_period",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "cash_ending_period",
        "cash_converted_ending_period",
        "account_unique_id",
        "cash_flow_type",
        "cash_flow_ordinal",
        "cash_beginning_period",
        "cash_net_period",
        "cash_converted_beginning_period",
        "cash_converted_net_period"
      ],
      "quickbooks__profit_and_loss": [
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_number",
        "account_ordinal"
      ],
      "quickbooks__balance_sheet": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__ap_ar_enhanced": [
        "transaction_type",
        "transaction_id",
        "doc_number",
        "transaction_with",
        "customer_vendor_name",
        "customer_vendor_balance",
        "customer_vendor_address_city",
        "customer_vendor_address_line",
        "customer_vendor_website",
        "total_amount",
        "total_converted_amount",
        "current_balance",
        "due_date",
        "is_overdue",
        "days_overdue",
        "initial_payment_date",
        "recent_payment_date",
        "total_current_payment",
        "total_current_converted_payment"
      ],
      "quickbooks__general_ledger_by_period": [
        "account_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_class",
        "class_id",
        "financial_statement_helper",
        "date_year",
        "period_first_day",
        "period_last_day",
        "period_net_change",
        "period_beginning_balance",
        "period_ending_balance",
        "period_net_converted_change",
        "period_beginning_converted_balance",
        "period_ending_converted_balance",
        "account_ordinal"
      ],
      "quickbooks__general_ledger": [
        "unique_id",
        "transaction_id",
        "transaction_index",
        "transaction_date",
        "customer_id",
        "vendor_id",
        "amount",
        "account_id",
        "class_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "financial_statement_helper",
        "account_current_balance",
        "account_class",
        "transaction_type",
        "transaction_source",
        "account_transaction_type",
        "created_at",
        "updated_at",
        "adjusted_amount",
        "adjusted_converted_amount",
        "running_balance",
        "running_converted_balance"
      ]
    }
  }
]
````