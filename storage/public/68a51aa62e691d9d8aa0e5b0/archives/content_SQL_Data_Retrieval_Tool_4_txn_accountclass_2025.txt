SQL Query:

````
SELECT DISTINCT transaction_type, account_class
FROM QuickBooks_Reports.quickbooks__general_ledger
WHERE transaction_date >= '2025-01-01' AND transaction_date <= '2025-12-31'
ORDER BY transaction_type ASC, account_class ASC;
````

Data retrieved successfully:

| transaction_type   | account_class   |
|:-------------------|:----------------|
| credit             |                 |
| credit             | Asset           |
| credit             | Expense         |
| credit             | Liability       |
| credit             | Revenue         |
| debit              |                 |
| debit              | Asset           |
| debit              | Expense         |
| debit              | Liability       |
| debit              | Revenue         |