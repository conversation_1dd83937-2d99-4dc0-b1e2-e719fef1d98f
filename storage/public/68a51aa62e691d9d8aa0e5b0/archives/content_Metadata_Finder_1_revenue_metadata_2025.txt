Metadata of all data sources:

````
[
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "invoice": {
        "table_name": "invoice",
        "fields": {
          "id": {
            "name": "id",
            "description": "Primary key uniquely identifying each invoice record in the system.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "71",
              "77",
              "128",
              "130",
              "132",
              "and 1682 more..."
            ]
          },
          "email_status": {
            "name": "email_status",
            "description": "Indicates whether an email notification for the invoice has been sent to the customer, with values such as 'NotSet' and 'EmailSent'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "NotSet",
              "EmailSent"
            ]
          },
          "print_status": {
            "name": "print_status",
            "description": "Tracks the printing state of the invoice, showing if it needs to be printed or if no print action has been taken.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "NotSet",
              "NeedToPrint"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp capturing when the invoice record was initially created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-30 19:28:22+00:00",
              "2018-03-30 19:23:24+00:00",
              "2018-03-30 19:26:21+00:00",
              "2018-03-30 20:08:44+00:00",
              "2018-03-30 19:25:22+00:00",
              "and 1682 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp of the latest modification made to the invoice record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-04-16 16:01:16+00:00",
              "2024-09-16 15:51:18+00:00",
              "2024-05-24 12:03:19+00:00",
              "2023-10-23 13:36:47+00:00",
              "2021-05-14 13:53:40+00:00",
              "and 1507 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The grand total billed on the invoice, including all line items and taxes, representing the full amount due.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1050",
              "2625",
              "3024",
              "4725",
              "6300",
              "and 724 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official accounting date assigned to the invoice, used for reporting and ledger entries.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-10-31",
              "2019-01-02",
              "2022-05-22",
              "2022-07-10",
              "2021-11-01",
              "and 867 more..."
            ]
          }
        }
      },
      "deposit": {
        "table_name": "deposit",
        "fields": {
          "id": {
            "name": "id",
            "description": "Primary key; uniquely identifies each deposit transaction record in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "6",
              "7",
              "1827",
              "2187",
              "and 28 more..."
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp marking when the deposit record was first created in the system for audit and historical purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-05-17 15:53:44+00:00",
              "2020-08-07 17:45:11+00:00",
              "2020-08-07 20:37:57+00:00",
              "2020-08-07 20:42:17+00:00",
              "2020-10-19 18:12:21+00:00",
              "and 28 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp showing the most recent modification to the deposit record, tracking change history.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-05-17 15:53:44+00:00",
              "2020-08-07 17:45:11+00:00",
              "2020-08-07 20:37:57+00:00",
              "2020-08-07 20:42:17+00:00",
              "2020-10-19 18:12:21+00:00",
              "and 28 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Total monetary value of the deposit transaction, stored with high precision for financial accuracy.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "125",
              "300",
              "21.37",
              "0.95",
              "and 26 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "Calendar date when the deposit transaction officially took place, used for accounting and reconciliation.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-10-31",
              "2025-05-30",
              "2020-03-31",
              "2021-01-29",
              "2020-08-31",
              "and 25 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n\n**Reasoning:**  \n\n\nTo calculate total revenue per transaction, we focus on transaction tables where revenue is recognized: `invoice` and `deposit`. `invoice.total_amount` and `deposit.total_amount` represent revenue. Dates are tracked by `transaction_date`. Status fields (for invoice: `email_status`, `print_status`) are needed to filter for valid/posted transactions. Business rules for revenue recognition should reference these status fields to include/exclude transactions for 2025.  \n\n\n**Table Columns:**  \n- table: invoice  \n  columns: id, total_amount, transaction_date, email_status, print_status, created_at, updated_at  \n- table: deposit  \n  columns: id, total_amount, transaction_date, created_at, updated_at  ",
    "other_table_columns": {
      "item": [
        "id",
        "fully_qualified_name",
        "name",
        "sync_token",
        "type",
        "sales_tax_included",
        "created_at",
        "updated_at",
        "income_account_id",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [
        "invoice_id",
        "index",
        "line_num",
        "description",
        "amount",
        "unit_price",
        "item_id",
        "class_id",
        "quantity",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [
        "id",
        "sync_token",
        "balance",
        "doc_number",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "due_date",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "estimate_line": [],
      "journal_entry_tax_line": [
        "journal_entry_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "term": [
        "id",
        "name",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [
        "purchase_id",
        "index",
        "amount",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "credit_card_payment_txn": [
        "id",
        "amount",
        "currency_id",
        "created_at",
        "updated_at",
        "transaction_date",
        "bank_account_id",
        "credit_card_account_id",
        "_fivetran_synced"
      ],
      "tax_code": [
        "id",
        "active",
        "description",
        "name",
        "taxable",
        "tax_group",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_type_applicable",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [
        "bill_id",
        "index",
        "bill_payment_id",
        "_fivetran_synced"
      ],
      "transfer": [
        "id",
        "amount",
        "private_note",
        "created_at",
        "updated_at",
        "currency_id",
        "transaction_date",
        "to_account_id",
        "from_account_id",
        "_fivetran_synced"
      ],
      "vendor": [
        "id",
        "family_name",
        "given_name",
        "company_name",
        "active",
        "balance",
        "display_name",
        "print_on_check_name",
        "middle_name",
        "sync_token",
        "billing_address_id",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [
        "invoice_id",
        "index",
        "payment_id",
        "_fivetran_synced"
      ],
      "deposit_line": [
        "deposit_id",
        "index",
        "description",
        "detail_type",
        "amount",
        "deposit_tax_applicable_on",
        "deposit_tax_code_id",
        "deposit_class_id",
        "deposit_account_id",
        "deposit_customer_id",
        "_fivetran_synced"
      ],
      "tax_agency": [
        "id",
        "display_name",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [
        "id",
        "pay_type",
        "doc_number",
        "sync_token",
        "currency_id",
        "check_print_status",
        "check_bank_account_id",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "account": [
        "id",
        "currency_id",
        "description",
        "fully_qualified_name",
        "account_type",
        "name",
        "active",
        "classification",
        "sub_account",
        "account_sub_type",
        "sync_token",
        "created_at",
        "updated_at",
        "balance_with_sub_accounts",
        "balance",
        "account_number",
        "parent_account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "address": [
        "id",
        "line_1",
        "line_2",
        "line_3",
        "line_4",
        "city",
        "postal_code",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [
        "id",
        "email_status",
        "doc_number",
        "sync_token",
        "global_tax_calculation",
        "shipping_address_id",
        "billing_address_id",
        "bill_email",
        "customer_memo",
        "total_tax",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "class": [
        "id",
        "sync_token",
        "fully_qualified_name",
        "name",
        "active",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bill_payment_line": [
        "bill_payment_id",
        "index",
        "amount",
        "bill_id",
        "journal_entry_id",
        "_fivetran_synced"
      ],
      "payment_line": [
        "payment_id",
        "index",
        "amount",
        "journal_entry_id",
        "credit_memo_id",
        "invoice_id",
        "_fivetran_synced"
      ],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "journal_entry": [
        "id",
        "doc_number",
        "sync_token",
        "private_note",
        "created_at",
        "updated_at",
        "transaction_date",
        "_fivetran_synced"
      ],
      "bill_line": [
        "bill_id",
        "index",
        "description",
        "amount",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "_fivetran_synced"
      ],
      "payment": [
        "id",
        "sync_token",
        "created_at",
        "updated_at",
        "reference_number",
        "unapplied_amount",
        "total_amount",
        "transaction_date",
        "payment_method_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "doc_number",
        "balance",
        "sync_token",
        "global_tax_calculation",
        "delivery_time",
        "billing_email",
        "customer_memo",
        "total_tax",
        "shipping_address_id",
        "billing_address_id",
        "due_date",
        "sales_term_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "purchase": [
        "id",
        "sync_token",
        "credit",
        "doc_number",
        "payment_type",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "customer_id",
        "vendor_id",
        "remit_to_address_id",
        "total_tax",
        "total_amount",
        "transaction_date",
        "account_id",
        "payment_method_id",
        "_fivetran_synced"
      ],
      "customer": [
        "id",
        "family_name",
        "fully_qualified_name",
        "given_name",
        "company_name",
        "display_name",
        "print_on_check_name",
        "title",
        "middle_name",
        "active",
        "sync_token",
        "balance_with_jobs",
        "balance",
        "taxable",
        "created_at",
        "updated_at",
        "email",
        "website",
        "phone_number",
        "mobile_number",
        "shipping_address_id",
        "bill_address_id",
        "default_tax_code_id",
        "sales_term_id",
        "_fivetran_synced"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "invoice_id",
        "index",
        "id",
        "line_num",
        "description",
        "amount",
        "detail_type",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_class_id",
        "sales_item_quantity",
        "sales_item_account_id",
        "sales_item_tax_code_id",
        "bundle_quantity",
        "_fivetran_synced"
      ],
      "credit_memo_line": [
        "credit_memo_id",
        "index",
        "description",
        "amount",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_account_id",
        "_fivetran_synced"
      ],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "journal_entry_line": [
        "journal_entry_id",
        "index",
        "description",
        "amount",
        "posting_type",
        "customer_id",
        "vendor_id",
        "tax_applicable_on",
        "tax_amount",
        "class_id",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "payment_method": [
        "id",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [
        "purchase_id",
        "index",
        "amount",
        "description",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "account_expense_customer_id",
        "_fivetran_synced"
      ],
      "deposit": [
        "sync_token",
        "private_note",
        "global_tax_calculation",
        "account_id",
        "_fivetran_synced"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Reports",
    "selected_table_column_metadata": {
      "quickbooks__expenses_sales_enhanced": {
        "table_name": "quickbooks__expenses_sales_enhanced",
        "fields": {
          "transaction_source": {
            "name": "transaction_source",
            "description": "Categorizes each transaction line as either an expense or a sale, enabling high-level separation for reporting and analytics.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "expense",
              "sales"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "System-generated identifier that groups all line items belonging to a single transaction; may repeat across multiple rows for multi-line transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "1158",
              "4723",
              "4918",
              "5445",
              "and 3938 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "User-facing reference or document number assigned to the transaction, such as an invoice or bill number; not guaranteed to be unique or always present.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "SVCCHRG",
              "ADJ448",
              "ADJ447",
              "ADJ445",
              "and 2487 more..."
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Enumerated value specifying the detailed classification of the transaction, such as 'invoice', 'bill', or 'journal_entry'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "journal_entry",
              "invoice",
              "bill",
              "purchase",
              "credit_memo",
              "deposit",
              "vendor_credit"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official date on which the transaction was recorded or occurred, used for period-based financial tracking.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2025-03-01",
              "2025-02-01",
              "2025-07-19",
              "and 1544 more..."
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Unique identifier for the customer related to this transaction line, populated mainly for sales or billable expense entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "7",
              "9",
              "10",
              "11",
              "14",
              "and 329 more..."
            ]
          },
          "customer_name": {
            "name": "customer_name",
            "description": "Name of the customer connected to the transaction line, typically found on sales-related records and may not be unique.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "G3 Canada Ltd",
              "Price Industries",
              "Adama Canada Ltd",
              "Arctic Glacier",
              "The Boyd Group",
              "and 326 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value of the line item in the transaction's original currency, reflecting the specific entry before conversion.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Monetary value of the line item converted to a standard reporting currency, supporting consolidated financial analysis.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Sum of all line items for the transaction in the original currency, representing the transaction's gross value.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "100",
              "375",
              "756",
              "2100",
              "and 1890 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "Total amount for the entire transaction converted into the reporting currency, enabling cross-currency comparisons.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "100",
              "375",
              "756",
              "2100",
              "and 1890 more..."
            ]
          }
        }
      },
      "quickbooks__profit_and_loss": {
        "table_name": "quickbooks__profit_and_loss",
        "fields": {
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the accounting period this record summarizes, marking the beginning of the reporting interval (e.g., month or quarter).",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-01",
              "2018-03-01",
              "2018-04-01",
              "2018-05-01",
              "2018-07-01",
              "and 96 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The ending date of the accounting period this record summarizes, marking the close of the reporting interval.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-31",
              "2018-03-31",
              "2018-04-30",
              "2018-05-31",
              "2018-07-31",
              "and 96 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "Indicates whether the transaction relates to revenue or expense, providing a primary split for profit and loss categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Revenue"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Specifies the high-level financial category of the account, such as 'Expense', 'Income', 'Other Expense', or 'Other Income', aligning with standard financial statement classifications.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Expense",
              "Other Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides detailed categorization within the account type, describing the specific nature or purpose of the account (e.g., 'OfficeGeneralAdministrativeExpenses').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "OfficeGeneralAdministrativeExpenses",
              "PayrollExpenses",
              "OtherMiscellaneousServiceCost",
              "AdvertisingPromotional",
              "OtherMiscellaneousExpense",
              "ServiceFeeIncome",
              "CostOfLabor",
              "Auto",
              "EntertainmentMeals",
              "BankCharges",
              "OtherPrimaryIncome",
              "Travel",
              "LegalProfessionalFees",
              "BadDebts",
              "UnappliedCashPaymentIncome",
              "Amortization",
              "TaxesPaid",
              "TravelMeals",
              "InterestPaid",
              "InterestEarned",
              "DiscountsRefundsGiven",
              "Insurance"
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "The unique string identifier for the account within the chart of accounts, used for referencing accounts; can be missing in some cases.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "4005",
              "5190",
              "5410",
              "5420",
              "5430",
              "and 64 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A system-generated unique identifier for the account, ensuring precise account reference within the accounting software.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "32",
              "and 74 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The human-readable name of the account, such as 'Marketing' or 'Office Space Income', used for clear identification in reports.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Wages & Salaries",
              "EI Expense",
              "Employee Benefits",
              "Temp Payroll",
              "CPP Expense",
              "and 72 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The original transaction amount recorded for this entry, which may be positive or negative depending on the financial activity.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "27",
              "100",
              "150",
              "and 2135 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "The transaction amount expressed in a standardized or reporting currency, enabling consolidated analysis across multi-currency records.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "27",
              "100",
              "150",
              "and 2135 more..."
            ]
          }
        }
      },
      "quickbooks__general_ledger": {
        "table_name": "quickbooks__general_ledger",
        "fields": {
          "transaction_id": {
            "name": "transaction_id",
            "description": "An identifier linking this ledger entry to a specific accounting transaction, grouping all related line items under the same transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "16",
              "21",
              "291",
              "1063",
              "and 6559 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The effective date on which the accounting transaction was recognized in the ledger.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2017-10-31",
              "2020-10-31",
              "2017-11-30",
              "and 1932 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The posted monetary value for this ledger entry, which may be positive or negative based on whether it represents a debit or credit.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "375",
              "500",
              "2500",
              "5000",
              "and 5834 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique reference identifier to the specific chart of accounts entry affected by this ledger line item.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "19",
              "30",
              "31",
              "55",
              "58",
              "and 123 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "The primary accounting category of the affected account, such as 'Expense', 'Asset', or 'Liability'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable",
              "Expense",
              "Bank",
              "Accounts Payable",
              "Income",
              "Other Current Asset",
              "Other Current Liability",
              "Credit Card",
              "Other Expense",
              "Fixed Asset",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "financial_statement_helper": {
            "name": "financial_statement_helper",
            "description": "Indicates whether the account is primarily reported on the 'balance_sheet' or 'income_statement', aiding in financial statement categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "balance_sheet",
              "income_statement"
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A high-level grouping of the account for financial reporting, such as 'Revenue', 'Asset', 'Expense', 'Liability', or 'Equity'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Expense",
              "Liability",
              "Revenue",
              "Equity"
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \n\n\nTo calculate total revenue per transaction for 2025, we need tables containing detailed revenue transactions, transaction date, and revenue status/channel fields. The most relevant are `quickbooks__expenses_sales_enhanced` (with line-level revenue details and status/channel fields) and `quickbooks__profit_and_loss` (for summarized revenue by period/account). We include metadata for revenue amount, transaction date, transaction status, and channel. Keys like `transaction_id` are required for grouping. Business rules regarding transaction status must also be noted.  \n\n\n  \n**Table Columns:**  \n\n\n- table: quickbooks__expenses_sales_enhanced  \n  columns: transaction_id, transaction_source, transaction_type, transaction_date, amount, converted_amount, total_amount, total_converted_amount, doc_number, customer_id, customer_name  \n  \n\n- table: quickbooks__profit_and_loss  \n  columns: account_class, account_type, account_sub_type, account_id, account_name, account_number, period_first_day, period_last_day, amount, converted_amount  \n  \n\n- table: quickbooks__general_ledger  \n  columns: transaction_id, transaction_date, amount, account_id, account_type, account_class, financial_statement_helper  \n  \n\n**(Business Rules: No explicit status or channel fields in tables, so \"transaction_source,\" \"transaction_type,\" and \"financial_statement_helper\" should be used to filter for valid revenue transactions. Only include records where transaction_type or account_type corresponds to revenue, for 2025-dated transactions.)**",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "transaction_line_id",
        "item_id",
        "item_quantity",
        "item_unit_price",
        "account_id",
        "account_name",
        "account_sub_type",
        "class_id",
        "customer_website",
        "vendor_id",
        "vendor_name",
        "billable_status",
        "description"
      ],
      "quickbooks__cash_flow_statement": [
        "cash_flow_period",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "cash_ending_period",
        "cash_converted_ending_period",
        "account_unique_id",
        "cash_flow_type",
        "cash_flow_ordinal",
        "cash_beginning_period",
        "cash_net_period",
        "cash_converted_beginning_period",
        "cash_converted_net_period"
      ],
      "quickbooks__profit_and_loss": [
        "calendar_date",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_ordinal"
      ],
      "quickbooks__balance_sheet": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__ap_ar_enhanced": [
        "transaction_type",
        "transaction_id",
        "doc_number",
        "transaction_with",
        "customer_vendor_name",
        "customer_vendor_balance",
        "customer_vendor_address_city",
        "customer_vendor_address_line",
        "customer_vendor_website",
        "total_amount",
        "total_converted_amount",
        "current_balance",
        "due_date",
        "is_overdue",
        "days_overdue",
        "initial_payment_date",
        "recent_payment_date",
        "total_current_payment",
        "total_current_converted_payment"
      ],
      "quickbooks__general_ledger_by_period": [
        "account_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_class",
        "class_id",
        "financial_statement_helper",
        "date_year",
        "period_first_day",
        "period_last_day",
        "period_net_change",
        "period_beginning_balance",
        "period_ending_balance",
        "period_net_converted_change",
        "period_beginning_converted_balance",
        "period_ending_converted_balance",
        "account_ordinal"
      ],
      "quickbooks__general_ledger": [
        "unique_id",
        "transaction_index",
        "customer_id",
        "vendor_id",
        "class_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_sub_type",
        "account_current_balance",
        "transaction_type",
        "transaction_source",
        "account_transaction_type",
        "created_at",
        "updated_at",
        "adjusted_amount",
        "adjusted_converted_amount",
        "running_balance",
        "running_converted_balance"
      ]
    }
  }
]
````