Metadata of all data sources:

````
[
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "bill": {
        "table_name": "bill",
        "fields": {
          "id": {
            "name": "id",
            "description": "The primary string-based key uniquely identifying each bill record in the system.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "380",
              "381",
              "383",
              "384",
              "386",
              "and 784 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "A versioning token indicating the current synchronization state of the bill record for concurrency and integration purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8"
            ]
          },
          "balance": {
            "name": "balance",
            "description": "The current outstanding amount yet to be paid on the bill, reflecting remaining liability after partial payments or credits.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2",
              "214",
              "5254.54",
              "2469.49",
              "and 16 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "The reference or document number assigned to the bill by the vendor, which may be non-unique or absent.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Starbucks",
              "QB",
              "Staples",
              "Impark",
              "Shaw",
              "and 342 more..."
            ]
          },
          "global_tax_calculation": {
            "name": "global_tax_calculation",
            "description": "Specifies how tax is calculated for the entire bill, indicating whether amounts are tax-exclusive, tax-inclusive, or if tax is not applied.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "TaxExcluded",
              "TaxInclusive",
              "NotApplicable"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp marking when the bill record was first created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-08-01 11:37:45+00:00",
              "2019-07-01 08:48:28+00:00",
              "2019-08-01 08:48:45+00:00",
              "2019-01-01 11:11:50+00:00",
              "2018-09-13 08:26:35+00:00",
              "and 760 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp indicating the most recent update made to the bill record, supporting audit trails and change tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2019-04-06 18:57:10+00:00",
              "2018-09-01 18:25:22+00:00",
              "2019-04-08 21:40:04+00:00",
              "2018-06-24 15:42:12+00:00",
              "2018-09-01 16:34:34+00:00",
              "and 415 more..."
            ]
          },
          "due_date": {
            "name": "due_date",
            "description": "The date by which payment for the bill is contractually required, used for managing payable deadlines.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-01",
              "2018-07-10",
              "2018-06-14",
              "2018-06-13",
              "2018-06-04",
              "and 469 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The full original amount of the bill, including all line items and applicable taxes, before any deductions or payments.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "100",
              "200",
              "250",
              "375",
              "500",
              "and 587 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The date the bill was issued or entered into the system, typically aligning with the vendor's invoice date for accounting.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-01",
              "2018-07-10",
              "2018-06-14",
              "2018-06-04",
              "2018-10-01",
              "and 470 more..."
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "Foreign key referencing the vendor entity from whom the bill originated, linking payables to specific suppliers.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "4",
              "5",
              "6",
              "34",
              "and 58 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "System-generated timestamp recording the latest synchronization time by Fivetran ETL processes for this record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:32.215000+00:00",
              "2025-08-06 22:09:32.231000+00:00",
              "2025-08-06 22:09:32.214000+00:00",
              "2025-08-06 22:09:32.223000+00:00",
              "2025-08-06 22:09:32.121000+00:00",
              "and 181 more..."
            ]
          }
        }
      },
      "bill_line": {
        "table_name": "bill_line",
        "fields": {
          "bill_id": {
            "name": "bill_id",
            "description": "Foreign key referencing the parent bill's unique identifier, linking each line item directly to its bill header.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1549",
              "2037",
              "2401",
              "3467",
              "3472",
              "and 784 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Line number indicating this item's sequence within its bill, uniquely identifying the line together with bill_id.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 11 more..."
            ]
          },
          "description": {
            "name": "description",
            "description": "Free-form text detailing the specific expense or purpose of this bill line item; may be blank or reused for similar charges.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "TD Visa July 10 2020",
              "Impark",
              "May Employee Benefits",
              "TD Visa Aug 11 2022",
              "TD Visa Oct 10 2023",
              "and 390 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value for this line item, positive for expenses and negative for credits or adjustments, stored with high precision.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "25",
              "27",
              "100",
              "175",
              "214",
              "and 1160 more..."
            ]
          },
          "account_expense_class_id": {
            "name": "account_expense_class_id",
            "description": "Optional foreign key to the 'class' table, representing the internal business class or department for this expense line.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1025694"
            ]
          },
          "account_expense_tax_code_id": {
            "name": "account_expense_tax_code_id",
            "description": "Foreign key identifying the tax code applicable to this expense line, determining tax calculation and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "3",
              "4",
              "5",
              "NON"
            ]
          },
          "account_expense_account_id": {
            "name": "account_expense_account_id",
            "description": "Foreign key to the general ledger account where this line's expense is recorded for financial tracking.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "22",
              "30",
              "31",
              "and 54 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "System-generated timestamp marking when this record was last imported into the data warehouse for ETL monitoring.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:32.221000+00:00",
              "2025-08-06 22:09:32.229000+00:00",
              "2025-08-06 22:09:32.219000+00:00",
              "2025-08-06 22:09:32.226000+00:00",
              "2025-08-06 22:09:32.222000+00:00",
              "and 183 more..."
            ]
          }
        }
      },
      "purchase": {
        "table_name": "purchase",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated unique identifier for each purchase record, serving as the table's primary key.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "230",
              "329",
              "584",
              "842",
              "843",
              "and 1128 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "Incremental value representing the current version of the record for concurrency and sync control.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "credit": {
            "name": "credit",
            "description": "Indicates if the purchase was made on credit (true) or not (false); typically used for purchases involving deferred payment.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "doc_number": {
            "name": "doc_number",
            "description": "Reference code or document number used to track or reconcile the purchase, which may be shared across multiple purchases.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "8",
              "9",
              "22",
              "and 184 more..."
            ]
          },
          "payment_type": {
            "name": "payment_type",
            "description": "Specifies the payment method category for the purchase, with allowed values: 'Check', 'Cash', or 'CreditCard'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Cash",
              "CreditCard",
              "Check"
            ]
          },
          "private_note": {
            "name": "private_note",
            "description": "Internal comment or note regarding the purchase, intended for private use and not guaranteed to be unique.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Service Charge",
              "HSA CAD CAN MSP",
              "CANADA LIFE CENTRE",
              "UBER CANADA/UBERTRIP",
              "BUS LINE FEE",
              "and 394 more..."
            ]
          },
          "global_tax_calculation": {
            "name": "global_tax_calculation",
            "description": "Method of tax application on the purchase: 'NotApplicable' (no tax), 'TaxInclusive' (tax included in total), or 'TaxExcluded' (tax added to subtotal).",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "TaxInclusive",
              "TaxExcluded",
              "NotApplicable"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp marking when the purchase record was first entered into the system.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-04-10 08:40:58+00:00",
              "2025-05-10 08:39:35+00:00",
              "2025-04-03 08:41:16+00:00",
              "2025-03-03 08:40:24+00:00",
              "2018-06-24 16:10:58+00:00",
              "and 1122 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp of the most recent modification to the purchase record, reflecting its last update.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-09-01 19:45:54+00:00",
              "2018-11-24 22:18:09+00:00",
              "2019-04-08 21:55:22+00:00",
              "2019-04-13 15:32:11+00:00",
              "2019-04-19 19:38:03+00:00",
              "and 1128 more..."
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Optional link to the customer associated with the purchase; foreign key to the customer table, rarely populated.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "24",
              "71",
              "125",
              "330",
              "538"
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "Foreign key referencing the vendor from whom the purchase was made; identifies the supplying party for the transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "35",
              "57",
              "81",
              "117",
              "263",
              "and 207 more..."
            ]
          },
          "remit_to_address_id": {
            "name": "remit_to_address_id",
            "description": "Foreign key pointing to the address designated for payment or correspondence related to the purchase, used in limited cases.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "101",
              "119",
              "290",
              "291"
            ]
          },
          "total_tax": {
            "name": "total_tax",
            "description": "Total calculated tax amount applied to the purchase, recorded as a high-precision numeric value.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "5",
              "2.7",
              "267.26",
              "526.69",
              "and 482 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Grand total of the purchase, including all charges and taxes, stored as a high-precision numeric value.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "150",
              "and 781 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "Calendar date on which the purchase transaction took place, used for chronological tracking.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2025-03-06",
              "2025-02-04",
              "2025-03-31",
              "2025-03-14",
              "2025-02-28",
              "and 506 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Foreign key to the general ledger account that the purchase is recorded against, indicating its accounting classification.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "54",
              "55",
              "106",
              "**********",
              "**********",
              "**********",
              "**********"
            ]
          },
          "payment_method_id": {
            "name": "payment_method_id",
            "description": "Optional foreign key referencing the specific payment method used for the purchase, such as a particular card or account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp of the last synchronization for this record by Fivetran ETL, used for data integration audit purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:07.206000+00:00",
              "2025-08-06 22:10:07.728000+00:00",
              "2025-08-06 22:10:07.727000+00:00",
              "2025-08-06 22:10:07.726000+00:00",
              "2025-08-06 22:10:07.725000+00:00",
              "and 102 more..."
            ]
          }
        }
      },
      "purchase_line": {
        "table_name": "purchase_line",
        "fields": {
          "purchase_id": {
            "name": "purchase_id",
            "description": "Foreign key referencing the parent purchase transaction; uniquely identifies which purchase this line item belongs to.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "4016",
              "4213",
              "4585",
              "4609",
              "5038",
              "and 1128 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Zero-based integer denoting the sequential position of this line item within its associated purchase, distinguishing multiple entries under the same purchase.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 4 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Exact monetary value for this line item, including charges, credits, or adjustments, stored as a high-precision decimal.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "54",
              "100",
              "125",
              "and 892 more..."
            ]
          },
          "description": {
            "name": "description",
            "description": "Optional free-text notes providing context or details specific to this purchase line item, such as vendor, purpose, or date.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "August Rent",
              "HSA CAD CAN MSP",
              "Talet acquistion",
              "CANADA LIFE CENTRE",
              "Jan Rent",
              "and 705 more..."
            ]
          },
          "account_expense_class_id": {
            "name": "account_expense_class_id",
            "description": "Optional foreign key linking this line item to a specific expense class or category for classification in reporting or accounting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1025694"
            ]
          },
          "account_expense_tax_code_id": {
            "name": "account_expense_tax_code_id",
            "description": "Code indicating the specific tax treatment (e.g., GST, PST, exempt) applied to this line item, supporting both numeric and 'NON' (no tax) values.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5",
              "7",
              "8",
              "NON"
            ]
          },
          "account_expense_account_id": {
            "name": "account_expense_account_id",
            "description": "Foreign key referencing the accounting ledger account to which the expense from this line item is posted.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "33",
              "and 52 more..."
            ]
          },
          "account_expense_customer_id": {
            "name": "account_expense_customer_id",
            "description": "Optional foreign key identifying a customer associated with this expense line, used primarily for billable or client-related expenses.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "24",
              "71",
              "125",
              "330",
              "538"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp recording the most recent synchronization of this record into the data warehouse via Fivetran ETL.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:06.957000+00:00",
              "2025-08-06 22:10:07.206000+00:00",
              "2025-08-06 22:10:07.205000+00:00",
              "2025-08-06 22:10:07.727000+00:00",
              "2025-08-06 22:10:07.726000+00:00",
              "and 101 more..."
            ]
          }
        }
      },
      "vendor": {
        "table_name": "vendor",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated primary key uniquely identifying each vendor in the database.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "3",
              "4",
              "5",
              "6",
              "33",
              "and 257 more..."
            ]
          },
          "family_name": {
            "name": "family_name",
            "description": "Last name of the vendor, populated for individual (non-company) vendors and may repeat across records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Mart",
              "Cafe",
              "Lutz",
              "De Jesus",
              "Chartrand",
              "and 93 more..."
            ]
          },
          "given_name": {
            "name": "given_name",
            "description": "First name of the vendor, used for individual vendors; not guaranteed to be unique or always present.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "The",
              "TD",
              "Manitoba",
              "Adam",
              "Victoria",
              "and 106 more..."
            ]
          },
          "company_name": {
            "name": "company_name",
            "description": "Registered business name for company or organizational vendors; left empty for individuals.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Freshwater Creative",
              "Artis 220 Portage Ltd",
              "Angela Chan",
              "Grand & Toy",
              "Talemetry Inc.",
              "and 173 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "Indicates if the vendor is currently enabled for transactions (true) or not (false).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "balance": {
            "name": "balance",
            "description": "Current monetary balance with the vendor, showing amounts owed to or by the vendor.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2",
              "-68.53",
              "1184.41",
              "180.25",
              "and 9 more..."
            ]
          },
          "display_name": {
            "name": "display_name",
            "description": "Unique name used in the application UI and reports to identify this vendor record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Thompson Dorfman Sweatman LLP",
              "Lurd, Kevin",
              "Freshwater Creative",
              "QuickBooks",
              "Artis 220 Portage Ltd",
              "and 257 more..."
            ]
          },
          "print_on_check_name": {
            "name": "print_on_check_name",
            "description": "Exact payee name to be printed on checks for this vendor, distinct from display or company name.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Thompson Dorfman Sweatman LLP",
              "Lurd, Kevin",
              "Freshwater Creative",
              "QuickBooks",
              "Artis 220 Portage Ltd",
              "and 257 more..."
            ]
          },
          "middle_name": {
            "name": "middle_name",
            "description": "Middle name of the vendor for individuals; optional and may not be unique.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "the",
              "Golf",
              "Liquor",
              "Car",
              "Fund",
              "and 21 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "Numeric version used internally to manage updates and concurrency for this vendor record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1"
            ]
          },
          "billing_address_id": {
            "name": "billing_address_id",
            "description": "Foreign key referencing the vendor's billing address in the address table; may be null if not provided.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5",
              "50",
              "51",
              "52",
              "53",
              "and 176 more..."
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp for when this vendor record was initially created in the system.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-03-21 16:41:12+00:00",
              "2025-07-02 15:42:22+00:00",
              "2018-03-30 14:34:18+00:00",
              "2018-05-19 17:46:29+00:00",
              "2018-03-30 15:39:29+00:00",
              "and 248 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp for the most recent modification made to this vendor record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-03-21 16:41:12+00:00",
              "2025-07-02 15:42:22+00:00",
              "2018-03-30 14:35:37+00:00",
              "2018-05-19 18:12:05+00:00",
              "2018-03-30 15:40:26+00:00",
              "and 250 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp recording the last successful ETL sync of this record by Fivetran into the data warehouse.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:13.548000+00:00",
              "2025-08-06 22:10:13.550000+00:00",
              "2025-08-06 22:10:13.549000+00:00",
              "2025-08-06 22:10:13.552000+00:00",
              "2025-08-06 22:10:13.551000+00:00",
              "and 5 more..."
            ]
          }
        }
      },
      "bill_payment": {
        "table_name": "bill_payment",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated unique identifier for each bill payment entry, used as the primary key for referencing specific payment records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "429",
              "583",
              "759",
              "781",
              "829",
              "and 407 more..."
            ]
          },
          "pay_type": {
            "name": "pay_type",
            "description": "Specifies the method used for the payment; always 'Check' in this table, indicating all payments are issued by check.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Check"
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "Reference number for the payment transaction, such as the check number; may be blank or shared by multiple records and is not guaranteed to be unique.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "96",
              "97",
              "98",
              "107",
              "and 213 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "An integer value representing the record\u2019s version or synchronization state, supporting safe updates and integration processes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "5"
            ]
          },
          "currency_id": {
            "name": "currency_id",
            "description": "Foreign key linking to the 'currency' table, always set to 'CAD' to denote that all bill payments are in Canadian Dollars.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "CAD"
            ]
          },
          "check_print_status": {
            "name": "check_print_status",
            "description": "Indicates the printing status of the payment check; always 'NotSet', reflecting that check printing is not tracked for these records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "NotSet"
            ]
          },
          "check_bank_account_id": {
            "name": "check_bank_account_id",
            "description": "Foreign key referencing the issuing bank account for the check payment, almost always '55', indicating a primary or default account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "55"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp for when the bill payment record was first created in the system, enabling chronological tracking of data entry.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-17 12:32:50+00:00",
              "2019-04-06 18:57:09+00:00",
              "2019-04-08 21:40:04+00:00",
              "2019-04-19 17:16:40+00:00",
              "2019-06-29 20:51:43+00:00",
              "and 407 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp for the most recent modification to the bill payment record, used for audit and change tracking purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-10-19 19:12:29+00:00",
              "2021-05-13 21:50:28+00:00",
              "2022-02-21 20:09:10+00:00",
              "2024-11-24 15:15:56+00:00",
              "2018-09-01 16:34:34+00:00",
              "and 397 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The exact monetary value paid to settle the associated bill(s) in this transaction, representing the full payment amount.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "100",
              "250",
              "300",
              "375",
              "500",
              "and 330 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The calendar date when the bill payment transaction was executed, used for financial reporting and reconciliation.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-01",
              "2019-08-01",
              "2018-01-26",
              "2018-03-01",
              "2021-10-08",
              "and 327 more..."
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "Foreign key linking to the 'vendor' table, identifying the vendor who received the bill payment; a vendor can have multiple associated payments.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "4",
              "5",
              "6",
              "34",
              "and 57 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp recording when this record was last imported into the data warehouse by Fivetran, used exclusively for ETL load auditing.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:08.418000+00:00",
              "2025-08-06 22:10:08.419000+00:00",
              "2025-08-06 22:10:08.423000+00:00",
              "2025-08-06 22:10:08.415000+00:00",
              "2025-08-06 22:10:08.416000+00:00",
              "and 17 more..."
            ]
          }
        }
      },
      "bill_payment_line": {
        "table_name": "bill_payment_line",
        "fields": {
          "bill_payment_id": {
            "name": "bill_payment_id",
            "description": "Unique identifier for the parent bill payment transaction to which this payment line belongs, linking each line to its corresponding bill payment record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "429",
              "583",
              "759",
              "781",
              "829",
              "and 404 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Sequential number indicating the order of this payment line within its associated bill payment, starting from 0 for the first line in the transaction.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 54 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value, with high precision, representing the specific portion of a bill paid by this payment line.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "100",
              "105",
              "200",
              "250",
              "375",
              "and 620 more..."
            ]
          },
          "bill_id": {
            "name": "bill_id",
            "description": "Identifier of the specific vendor bill being paid or partially paid by this line; may be empty if the payment line is not linked to a bill.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "965",
              "966",
              "990",
              "1046",
              "1146",
              "and 774 more..."
            ]
          },
          "journal_entry_id": {
            "name": "journal_entry_id",
            "description": "Reference to the associated journal entry for this payment line, used for accounting reconciliation and special financial cases; often blank except for particular scenarios.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2327",
              "2832",
              "3270",
              "5018",
              "5219"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp marking when this record was last synchronized to the database by Fivetran, used to monitor data update recency.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:08.414000+00:00",
              "2025-08-06 22:10:08.412000+00:00",
              "2025-08-06 22:10:08.413000+00:00",
              "2025-08-06 22:10:08.415000+00:00",
              "2025-08-06 22:10:08.411000+00:00",
              "and 17 more..."
            ]
          }
        }
      },
      "bill_linked_txn": {
        "table_name": "bill_linked_txn",
        "fields": {
          "bill_id": {
            "name": "bill_id",
            "description": "Unique identifier of the bill in this payment linkage, referencing the 'bill' table and indicating which vendor bill is being connected to a payment.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1625",
              "1710",
              "2223",
              "2224",
              "2383",
              "and 774 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Ordinal number distinguishing each distinct payment link for the same bill, ensuring uniqueness when combined with 'bill_id'.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "bill_payment_id": {
            "name": "bill_payment_id",
            "description": "Unique identifier of the bill payment associated with this linkage, referencing the 'bill_payment' table to specify which payment transaction is applied to the bill.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "429",
              "583",
              "759",
              "781",
              "829",
              "and 403 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp recording the most recent synchronization of this row into the data warehouse by Fivetran, used for data freshness and audit purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:32.215000+00:00",
              "2025-08-06 22:09:32.223000+00:00",
              "2025-08-06 22:09:32.231000+00:00",
              "2025-08-06 22:09:32.206000+00:00",
              "2025-08-06 22:09:32.214000+00:00",
              "and 178 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \n\n\nTo answer the question, I identified all tables storing:  \n- Vendor bills and expenses: `bill`, `bill_line`, `purchase`, `purchase_line`  \n- Vendors: `vendor`  \n- Bank payments and allocations: `bill_payment`, `bill_payment_line`, and the linking table `bill_linked_txn`  \n- Relationships/allocations: `bill_payment_line` (allocates payments to bills), `bill_linked_txn` (links bills to payments and tracks partial/multiple payments)  \nI also included business rules and ER diagram as requested. Primary and foreign keys for joins are listed for all.  \n\n\n  \n**Table Columns:**  \n\n\n- table: bill  \n  columns: id, sync_token, balance, doc_number, global_tax_calculation, created_at, updated_at, due_date, total_amount, transaction_date, vendor_id, _fivetran_synced  \n\n- table: bill_line  \n  columns: bill_id, index, description, amount, account_expense_class_id, account_expense_tax_code_id, account_expense_account_id, _fivetran_synced  \n\n- table: purchase  \n  columns: id, sync_token, credit, doc_number, payment_type, private_note, global_tax_calculation, created_at, updated_at, customer_id, vendor_id, remit_to_address_id, total_tax, total_amount, transaction_date, account_id, payment_method_id, _fivetran_synced  \n\n- table: purchase_line  \n  columns: purchase_id, index, amount, description, account_expense_class_id, account_expense_tax_code_id, account_expense_account_id, account_expense_customer_id, _fivetran_synced  \n\n- table: vendor  \n  columns: id, family_name, given_name, company_name, active, balance, display_name, print_on_check_name, middle_name, sync_token, billing_address_id, created_at, updated_at, _fivetran_synced  \n\n- table: bill_payment  \n  columns: id, pay_type, doc_number, sync_token, currency_id, check_print_status, check_bank_account_id, created_at, updated_at, total_amount, transaction_date, vendor_id, _fivetran_synced  \n\n- table: bill_payment_line  \n  columns: bill_payment_id, index, amount, bill_id, journal_entry_id, _fivetran_synced  \n\n- table: bill_linked_txn  \n  columns: bill_id, index, bill_payment_id, _fivetran_synced  \n\n**Primary and Foreign Keys for Joins:**  \n- bill: id (PK), vendor_id (FK \u2192 vendor.id)  \n- bill_line: (bill_id, index) (PK), bill_id (FK \u2192 bill.id), account_expense_account_id (FK \u2192 account.id), account_expense_class_id (FK \u2192 class.id), account_expense_tax_code_id (FK \u2192 tax_code.id)  \n- purchase: id (PK), vendor_id (FK \u2192 vendor.id), customer_id (FK \u2192 customer.id), payment_method_id (FK \u2192 payment_method.id), account_id (FK \u2192 account.id)  \n- purchase_line: (purchase_id, index) (PK), purchase_id (FK \u2192 purchase.id), account_expense_account_id (FK \u2192 account.id), account_expense_class_id (FK \u2192 class.id)  \n- vendor: id (PK), billing_address_id (FK \u2192 address.id)  \n- bill_payment: id (PK), vendor_id (FK \u2192 vendor.id), check_bank_account_id (FK \u2192 account.id)  \n- bill_payment_line: (bill_payment_id, index) (PK), bill_payment_id (FK \u2192 bill_payment.id), bill_id (FK \u2192 bill.id), journal_entry_id (FK \u2192 journal_entry.id)  \n- bill_linked_txn: (bill_id, index) (PK), bill_id (FK \u2192 bill.id), bill_payment_id (FK \u2192 bill_payment.id)  \n\n\n**Business Rules:**  \n- Each `bill` is from a single `vendor` (vendor_id FK).  \n- `bill_line` contains the line items/expenses per bill; links to `bill` and to ledger accounts.  \n- `purchase` and `purchase_line` capture direct vendor expenses outside the bill-pay process.  \n- `bill_payment` records payments (typically issued via check) to vendors.  \n- `bill_payment_line` allocates payment amounts to specific bills (partial or full payment possible).  \n- `bill_linked_txn` provides a join between bills and payments for situations where payments cover multiple bills or bills are paid by multiple payments.  \n- Status for paid/unpaid bills is tracked by the `balance` column in `bill`.  \n- Payment allocations and multiple-to-multiple relationships are resolved via `bill_payment_line` and `bill_linked_txn`.  \n\n**ER Diagram:**  \n- Provided as JSON in the supplied ER diagram (see original input for all relationships and keys).  \n- Key relationships:\n  - bill \u2194 bill_line (1-to-many)\n  - bill \u2194 vendor (many bills per vendor)\n  - bill \u2194 bill_payment_line (via bill_id)\n  - bill_payment \u2194 bill_payment_line (via bill_payment_id)\n  - bill_payment \u2194 vendor (many payments per vendor)\n  - bill_linked_txn links bill and bill_payment (for multiple allocations)\n  - purchase \u2194 purchase_line (1-to-many)\n  - purchase \u2194 vendor (many purchases per vendor)\n\nAll metadata, business rules, and keys are available as above, with full ER diagram structure in the JSON provided.",
    "other_table_columns": {
      "item": [
        "id",
        "fully_qualified_name",
        "name",
        "sync_token",
        "type",
        "sales_tax_included",
        "created_at",
        "updated_at",
        "income_account_id",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [
        "invoice_id",
        "index",
        "line_num",
        "description",
        "amount",
        "unit_price",
        "item_id",
        "class_id",
        "quantity",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "vendor_credit_line": [],
      "time_activity": [],
      "estimate_line": [],
      "journal_entry_tax_line": [
        "journal_entry_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "term": [
        "id",
        "name",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [
        "purchase_id",
        "index",
        "amount",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "credit_card_payment_txn": [
        "id",
        "amount",
        "currency_id",
        "created_at",
        "updated_at",
        "transaction_date",
        "bank_account_id",
        "credit_card_account_id",
        "_fivetran_synced"
      ],
      "tax_code": [
        "id",
        "active",
        "description",
        "name",
        "taxable",
        "tax_group",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_type_applicable",
        "_fivetran_synced"
      ],
      "transfer": [
        "id",
        "amount",
        "private_note",
        "created_at",
        "updated_at",
        "currency_id",
        "transaction_date",
        "to_account_id",
        "from_account_id",
        "_fivetran_synced"
      ],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [
        "invoice_id",
        "index",
        "payment_id",
        "_fivetran_synced"
      ],
      "deposit_line": [
        "deposit_id",
        "index",
        "description",
        "detail_type",
        "amount",
        "deposit_tax_applicable_on",
        "deposit_tax_code_id",
        "deposit_class_id",
        "deposit_account_id",
        "deposit_customer_id",
        "_fivetran_synced"
      ],
      "tax_agency": [
        "id",
        "display_name",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "account": [
        "id",
        "currency_id",
        "description",
        "fully_qualified_name",
        "account_type",
        "name",
        "active",
        "classification",
        "sub_account",
        "account_sub_type",
        "sync_token",
        "created_at",
        "updated_at",
        "balance_with_sub_accounts",
        "balance",
        "account_number",
        "parent_account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "address": [
        "id",
        "line_1",
        "line_2",
        "line_3",
        "line_4",
        "city",
        "postal_code",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [
        "id",
        "email_status",
        "doc_number",
        "sync_token",
        "global_tax_calculation",
        "shipping_address_id",
        "billing_address_id",
        "bill_email",
        "customer_memo",
        "total_tax",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "class": [
        "id",
        "sync_token",
        "fully_qualified_name",
        "name",
        "active",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "payment_line": [
        "payment_id",
        "index",
        "amount",
        "journal_entry_id",
        "credit_memo_id",
        "invoice_id",
        "_fivetran_synced"
      ],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "journal_entry": [
        "id",
        "doc_number",
        "sync_token",
        "private_note",
        "created_at",
        "updated_at",
        "transaction_date",
        "_fivetran_synced"
      ],
      "payment": [
        "id",
        "sync_token",
        "created_at",
        "updated_at",
        "reference_number",
        "unapplied_amount",
        "total_amount",
        "transaction_date",
        "payment_method_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "id",
        "email_status",
        "doc_number",
        "print_status",
        "balance",
        "sync_token",
        "global_tax_calculation",
        "delivery_time",
        "created_at",
        "updated_at",
        "billing_email",
        "customer_memo",
        "total_tax",
        "shipping_address_id",
        "billing_address_id",
        "due_date",
        "total_amount",
        "transaction_date",
        "sales_term_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "customer": [
        "id",
        "family_name",
        "fully_qualified_name",
        "given_name",
        "company_name",
        "display_name",
        "print_on_check_name",
        "title",
        "middle_name",
        "active",
        "sync_token",
        "balance_with_jobs",
        "balance",
        "taxable",
        "created_at",
        "updated_at",
        "email",
        "website",
        "phone_number",
        "mobile_number",
        "shipping_address_id",
        "bill_address_id",
        "default_tax_code_id",
        "sales_term_id",
        "_fivetran_synced"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "invoice_id",
        "index",
        "id",
        "line_num",
        "description",
        "amount",
        "detail_type",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_class_id",
        "sales_item_quantity",
        "sales_item_account_id",
        "sales_item_tax_code_id",
        "bundle_quantity",
        "_fivetran_synced"
      ],
      "credit_memo_line": [
        "credit_memo_id",
        "index",
        "description",
        "amount",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_account_id",
        "_fivetran_synced"
      ],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "journal_entry_line": [
        "journal_entry_id",
        "index",
        "description",
        "amount",
        "posting_type",
        "customer_id",
        "vendor_id",
        "tax_applicable_on",
        "tax_amount",
        "class_id",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "payment_method": [
        "id",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "deposit": [
        "id",
        "sync_token",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "account_id",
        "_fivetran_synced"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Reports",
    "selected_table_column_metadata": {
      "quickbooks__expenses_sales_enhanced": {
        "table_name": "quickbooks__expenses_sales_enhanced",
        "fields": {
          "transaction_source": {
            "name": "transaction_source",
            "description": "Categorizes each transaction line as either an expense or a sale, enabling high-level separation for reporting and analytics.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "expense",
              "sales"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "System-generated identifier that groups all line items belonging to a single transaction; may repeat across multiple rows for multi-line transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "1158",
              "4723",
              "4918",
              "5445",
              "and 3938 more..."
            ]
          },
          "transaction_line_id": {
            "name": "transaction_line_id",
            "description": "Unique line item number within a specific transaction, used with 'transaction_id' to pinpoint individual transaction lines.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 37 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "User-facing reference or document number assigned to the transaction, such as an invoice or bill number; not guaranteed to be unique or always present.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "SVCCHRG",
              "ADJ448",
              "ADJ447",
              "ADJ445",
              "and 2487 more..."
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Enumerated value specifying the detailed classification of the transaction, such as 'invoice', 'bill', or 'journal_entry'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "journal_entry",
              "invoice",
              "bill",
              "purchase",
              "credit_memo",
              "deposit",
              "vendor_credit"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official date on which the transaction was recorded or occurred, used for period-based financial tracking.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2025-03-01",
              "2025-02-01",
              "2025-07-19",
              "and 1544 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Unique identifier for the general ledger (GL) account linked to this line, used for accounting and reporting purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "33",
              "and 67 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "Descriptive name of the GL account associated with the transaction line, aiding in categorization of financial activity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Perm Revenue",
              "Office Expenses",
              "Temp Revenue",
              "Meals and entertainment",
              "Wages & Salaries",
              "and 65 more..."
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Detailed sub-category of the GL account, offering finer granularity for accounting and expense tracking.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "OfficeGeneralAdministrativeExpenses",
              "ServiceFeeIncome",
              "PayrollExpenses",
              "EntertainmentMeals",
              "CostOfLabor",
              "BankCharges",
              "OtherMiscellaneousServiceCost",
              "AdvertisingPromotional",
              "OtherMiscellaneousExpense",
              "Travel",
              "Auto",
              "LegalProfessionalFees",
              "TaxesPaid",
              "Insurance",
              "TravelMeals",
              "InterestPaid",
              "BadDebts",
              "Amortization"
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "Unique identifier for the vendor or supplier associated with the line item, particularly relevant to expense or purchase transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "4",
              "35",
              "57",
              "78",
              "and 236 more..."
            ]
          },
          "vendor_name": {
            "name": "vendor_name",
            "description": "Name of the vendor or supplier linked to the line, present mainly for expense-related records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "TD Visa-1077",
              "Canada Life",
              "Harvard Property Management",
              "Brittani Lutz",
              "Vuppala Info Systems",
              "and 236 more..."
            ]
          },
          "billable_status": {
            "name": "billable_status",
            "description": "Indicates if the line is billable to a customer; always set to 'NotBillable' in this dataset.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "NotBillable"
            ]
          },
          "description": {
            "name": "description",
            "description": "Optional free-text field capturing additional notes or details about the transaction line, such as merchant names or explanations.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "CPP/QPP Employer",
              "EI Employer",
              "Impark",
              "Wages",
              "August Rent",
              "and 3242 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value of the line item in the transaction's original currency, reflecting the specific entry before conversion.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Monetary value of the line item converted to a standard reporting currency, supporting consolidated financial analysis.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Sum of all line items for the transaction in the original currency, representing the transaction's gross value.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "100",
              "375",
              "756",
              "2100",
              "and 1890 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "Total amount for the entire transaction converted into the reporting currency, enabling cross-currency comparisons.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "100",
              "375",
              "756",
              "2100",
              "and 1890 more..."
            ]
          }
        }
      },
      "quickbooks__ap_ar_enhanced": {
        "table_name": "quickbooks__ap_ar_enhanced",
        "fields": {
          "transaction_type": {
            "name": "transaction_type",
            "description": "Classifies the financial transaction as either an 'invoice' for accounts receivable or a 'bill' for accounts payable, distinguishing between customer and vendor transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "invoice",
              "bill"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique system-generated identifier for each invoice or bill, serving as the table's primary key for tracking individual transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "556",
              "594",
              "880",
              "966",
              "1010",
              "and 2471 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "The external or reference document number assigned to the invoice or bill, used for reconciliation and client/vendor correspondence; may be absent or occasionally duplicated.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Starbucks",
              "QB",
              "Staples",
              "Impark",
              "Shaw",
              "and 2028 more..."
            ]
          },
          "transaction_with": {
            "name": "transaction_with",
            "description": "Indicates whether the transaction is with a 'customer' (receivable) or 'vendor' (payable), enabling segmentation by counterparty type.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "customer",
              "vendor"
            ]
          },
          "customer_vendor_name": {
            "name": "customer_vendor_name",
            "description": "The full name of the customer or vendor involved in the transaction, identifying the specific business or individual counterpart.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "TD Visa-1077",
              "Price Industries",
              "G3 Canada Ltd",
              "Adama Canada Ltd",
              "Canada Life",
              "and 433 more..."
            ]
          },
          "customer_vendor_balance": {
            "name": "customer_vendor_balance",
            "description": "The running balance owed by or to the named customer or vendor at the time of the transaction, reflecting their overall account status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2",
              "10500",
              "15750",
              "21000",
              "and 52 more..."
            ]
          },
          "customer_vendor_address_city": {
            "name": "customer_vendor_address_city",
            "description": "The city from the address of the customer or vendor, provided when available, for geographic reporting and analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Winnipeg",
              "West St. Paul",
              "Toronto",
              "Oak Bluff",
              "Montreal",
              "Blumenort",
              "Rosser",
              "Altona",
              "Rosenort",
              "Headingley",
              "Springfield",
              "Morden",
              "Grande Pointe",
              "Sunnyside",
              "Morris",
              "Regina",
              "Winkler",
              "Arborg",
              "Saskatoon",
              "Steinbach",
              "West Saint Paul"
            ]
          },
          "customer_vendor_address_line": {
            "name": "customer_vendor_address_line",
            "description": "The street address or main address line for the customer or vendor, included in a minority of records for detailed location reference.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Edison Properties401-10 Fort Street",
              "Johanna ChipmanControlled Environments Limited",
              "Laura Guth-MazolleckBird Construction Group",
              "Kathy MacDonaldControlled Environments Limited",
              "G3 Canada Ltd200 Portage Avenue",
              "and 39 more..."
            ]
          },
          "customer_vendor_website": {
            "name": "customer_vendor_website",
            "description": "The website URL associated with the customer or vendor, if known, for contact or business verification purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "http://www.edisonproperties.ca",
              "http://www.mbtechaccelerator.com"
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The full monetary value of the invoice or bill in its original transaction currency, representing the total amount charged or billed.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "756",
              "2100",
              "2625",
              "3024",
              "4725",
              "and 1292 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "The transaction amount converted from the original currency to a standard base currency, supporting consolidated financial reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "756",
              "2100",
              "2625",
              "3024",
              "4725",
              "and 1292 more..."
            ]
          },
          "current_balance": {
            "name": "current_balance",
            "description": "The outstanding unpaid amount on the invoice or bill at the reporting date, representing what is still owed.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "8400",
              "8820",
              "10500",
              "11088",
              "and 81 more..."
            ]
          },
          "due_date": {
            "name": "due_date",
            "description": "The date when payment is contractually due for the invoice or bill, central to overdue and cash flow analysis.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-11-30",
              "2019-02-01",
              "2022-06-01",
              "2018-07-18",
              "2018-11-01",
              "and 1180 more..."
            ]
          },
          "is_overdue": {
            "name": "is_overdue",
            "description": "A boolean flag indicating if the invoice or bill remains unpaid past its due date as of the current reporting period.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "days_overdue": {
            "name": "days_overdue",
            "description": "The number of days the transaction has been overdue, calculated from the due date to the reporting date; zero if not overdue.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "8",
              "11",
              "14",
              "19",
              "and 15 more..."
            ]
          },
          "initial_payment_date": {
            "name": "initial_payment_date",
            "description": "The date on which the first payment was applied toward this invoice or bill, marking the beginning of payment activity.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-01",
              "2018-07-30",
              "2018-11-28",
              "2018-04-30",
              "2018-06-26",
              "and 1015 more..."
            ]
          },
          "recent_payment_date": {
            "name": "recent_payment_date",
            "description": "The date of the latest payment received or made for this transaction, capturing the most recent payment action.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-01",
              "2018-11-28",
              "2018-07-30",
              "2018-04-30",
              "2018-06-26",
              "and 1018 more..."
            ]
          },
          "total_current_payment": {
            "name": "total_current_payment",
            "description": "The sum of all payments made toward the transaction, expressed in the original transaction currency, for payment tracking.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "756",
              "2100",
              "2625",
              "3024",
              "and 1268 more..."
            ]
          },
          "total_current_converted_payment": {
            "name": "total_current_converted_payment",
            "description": "The total payments received or made for the transaction, converted to the base currency for unified payment reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "756",
              "2100",
              "2625",
              "3024",
              "and 1268 more..."
            ]
          }
        }
      },
      "quickbooks__general_ledger": {
        "table_name": "quickbooks__general_ledger",
        "fields": {
          "unique_id": {
            "name": "unique_id",
            "description": "A system-generated primary key that uniquely identifies each individual general ledger line item in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "177761637a1e9da971584f556c12fe54",
              "6121a0bf392766b81cff0d01569de02e",
              "aa7f2f904eb74faf0a38f71b6ce1a147",
              "7701a8814098f05c67dd1908ad4c5cb8",
              "5516b1e25a4d65c8d6d52f614f7eb224",
              "and 25457 more..."
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "An identifier linking this ledger entry to a specific accounting transaction, grouping all related line items under the same transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "16",
              "21",
              "291",
              "1063",
              "and 6559 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The effective date on which the accounting transaction was recognized in the ledger.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2017-10-31",
              "2020-10-31",
              "2017-11-30",
              "and 1932 more..."
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "A reference to the vendor involved in the transaction, included only for entries directly associated with a vendor.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "4",
              "5",
              "6",
              "35",
              "and 248 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The posted monetary value for this ledger entry, which may be positive or negative based on whether it represents a debit or credit.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "375",
              "500",
              "2500",
              "5000",
              "and 5834 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique reference identifier to the specific chart of accounts entry affected by this ledger line item.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "19",
              "30",
              "31",
              "55",
              "58",
              "and 123 more..."
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "An optional code used to assign additional categorization, such as department or project, to the ledger entry for enhanced reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "The formal account number from the chart of accounts associated with this ledger line item.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1200",
              "1210",
              "1220",
              "1330",
              "and 106 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The descriptive name of the specific account impacted by this ledger entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Chequing",
              "Accounts Receivable (A/R)",
              "Corporate Tax Receivable",
              "Advances & Loans",
              "Accounts Payable (A/P)",
              "and 121 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "The primary accounting category of the affected account, such as 'Expense', 'Asset', or 'Liability'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable",
              "Expense",
              "Bank",
              "Accounts Payable",
              "Income",
              "Other Current Asset",
              "Other Current Liability",
              "Credit Card",
              "Other Expense",
              "Fixed Asset",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "A more specific classification within the account type, detailing the nature of the account (e.g., 'OfficeGeneralAdministrativeExpenses').",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "AccountsReceivable",
              "Checking",
              "AccountsPayable",
              "OfficeGeneralAdministrativeExpenses",
              "ServiceFeeIncome",
              "and 39 more..."
            ]
          },
          "financial_statement_helper": {
            "name": "financial_statement_helper",
            "description": "Indicates whether the account is primarily reported on the 'balance_sheet' or 'income_statement', aiding in financial statement categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "balance_sheet",
              "income_statement"
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A high-level grouping of the account for financial reporting, such as 'Revenue', 'Asset', 'Expense', 'Liability', or 'Equity'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Expense",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Specifies whether this ledger line is a debit or a credit, defining its effect on the account balance.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "transaction_source": {
            "name": "transaction_source",
            "description": "Describes the origin or method by which the transaction was created in the accounting system (e.g., 'bill').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "invoice",
              "payment",
              "journal_entry",
              "bill",
              "purchase",
              "bill payment",
              "deposit",
              "credit card payment",
              "credit_memo",
              "transfer",
              "vendor_credit"
            ]
          },
          "account_transaction_type": {
            "name": "account_transaction_type",
            "description": "Indicates the nature of the transaction's impact on the account, specifying if it is a debit or credit from the account's perspective.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "The timestamp recording when this ledger entry was first created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-02-24 17:52:31+00:00",
              "2021-02-23 23:59:46+00:00",
              "2018-04-15 14:47:54+00:00",
              "2018-11-24 21:28:52+00:00",
              "2018-02-24 19:14:04+00:00",
              "and 6421 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "The timestamp indicating the most recent update or modification to this ledger entry.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-10-19 19:12:29+00:00",
              "2019-04-06 18:57:10+00:00",
              "2018-09-01 18:25:22+00:00",
              "2018-06-24 15:42:12+00:00",
              "2019-04-08 21:40:04+00:00",
              "and 4384 more..."
            ]
          },
          "adjusted_amount": {
            "name": "adjusted_amount",
            "description": "The monetary value for this entry after any corrections or adjustments have been applied, which may differ from the original amount.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2500",
              "3123",
              "8000",
              "8400",
              "and 7488 more..."
            ]
          },
          "adjusted_converted_amount": {
            "name": "adjusted_converted_amount",
            "description": "The entry\u2019s monetary value after adjustments, expressed in a standardized or reporting currency for cross-currency consistency.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2500",
              "3123",
              "8000",
              "8400",
              "and 7488 more..."
            ]
          },
          "running_balance": {
            "name": "running_balance",
            "description": "The cumulative account balance immediately after this transaction in the account\u2019s original currency, showing the sequential change over time.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "200",
              "160536.99",
              "28349.45",
              "-212316.07",
              "and 19558 more..."
            ]
          },
          "running_converted_balance": {
            "name": "running_converted_balance",
            "description": "The cumulative account balance after this transaction, expressed in the reporting or base currency, incorporating currency conversions and adjustments.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "200",
              "160536.99",
              "28349.45",
              "-212316.07",
              "and 19558 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n  \n**Reasoning:**  \nTo answer the question, I identified tables related to vendor bills, expenses, vendor master data, payments, and payment allocations by reviewing descriptions and business rules. I selected all columns from tables that hold detailed bill, expense, vendor, and payment records, as well as those needed for payment allocations (e.g., linking payments to multiple bills). I included all primary and foreign keys, and referenced the ER diagram for table relationships.  \n  \n  \n---  \n  \n**Table Columns:**   \n- table: quickbooks__expenses_sales_enhanced  \n  columns: transaction_id, transaction_line_id, vendor_id, vendor_name, transaction_source, transaction_type, transaction_date, doc_number, account_id, account_name, account_sub_type, description, amount, converted_amount, total_amount, total_converted_amount, billable_status  \n  \n- table: quickbooks__ap_ar_enhanced  \n  columns: transaction_id, transaction_type, doc_number, transaction_with, customer_vendor_name, customer_vendor_balance, customer_vendor_address_city, customer_vendor_address_line, customer_vendor_website, total_amount, total_converted_amount, current_balance, due_date, is_overdue, days_overdue, initial_payment_date, recent_payment_date, total_current_payment, total_current_converted_payment  \n  \n- table: quickbooks__general_ledger  \n  columns: unique_id, transaction_id, vendor_id, transaction_date, amount, account_id, account_number, account_name, account_type, account_sub_type, account_class, financial_statement_helper, transaction_type, transaction_source, account_transaction_type, running_balance, running_converted_balance, created_at, updated_at, adjusted_amount, adjusted_converted_amount, class_id  \n  \n  \n---  \n  \n**Business Rules:**  \n- transaction_id links bills, purchases, invoices, or deposits in detail tables to the general ledger (one-to-many).  \n- vendor_id links to vendor master data.  \n- Payment allocations and status flags (e.g., is_overdue, days_overdue, current_balance) are in quickbooks__ap_ar_enhanced.  \n  \n  \n---  \n  \n**ER Diagram:**  \n- quickbooks__expenses_sales_enhanced: PK (transaction_id, transaction_line_id)  \n- quickbooks__ap_ar_enhanced: PK (transaction_id)  \n- quickbooks__general_ledger: PK (unique_id)  \n- Relationships: transaction_id is used to join transactions (bills, purchases, invoices, deposits) to the general ledger.  \n  \n  \n---",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "item_id",
        "item_quantity",
        "item_unit_price",
        "class_id",
        "customer_id",
        "customer_name",
        "customer_website"
      ],
      "quickbooks__cash_flow_statement": [
        "cash_flow_period",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "cash_ending_period",
        "cash_converted_ending_period",
        "account_unique_id",
        "cash_flow_type",
        "cash_flow_ordinal",
        "cash_beginning_period",
        "cash_net_period",
        "cash_converted_beginning_period",
        "cash_converted_net_period"
      ],
      "quickbooks__profit_and_loss": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__balance_sheet": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__general_ledger_by_period": [
        "account_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_class",
        "class_id",
        "financial_statement_helper",
        "date_year",
        "period_first_day",
        "period_last_day",
        "period_net_change",
        "period_beginning_balance",
        "period_ending_balance",
        "period_net_converted_change",
        "period_beginning_converted_balance",
        "period_ending_converted_balance",
        "account_ordinal"
      ],
      "quickbooks__general_ledger": [
        "transaction_index",
        "customer_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_current_balance"
      ]
    }
  }
]
````