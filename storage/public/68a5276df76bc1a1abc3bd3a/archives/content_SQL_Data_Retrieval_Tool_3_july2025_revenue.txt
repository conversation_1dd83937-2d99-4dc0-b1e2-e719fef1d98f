SQL Query:

````
SELECT
  DATE '2025-07-01' AS month_start,
  SUM(
    CASE
      WHEN transaction_type = 'credit' AND (account_class IS NULL OR account_class = '') THEN -adjusted_amount
      WHEN account_class = 'Revenue' THEN adjusted_amount
      ELSE 0
    END
  ) AS total_revenue
FROM
  quickbooks__general_ledger
WHERE
  transaction_date BETWEEN DATE '2025-07-01' AND DATE '2025-07-31';
````

Data retrieved successfully:

| month_start             |   total_revenue |
|:------------------------|----------------:|
| {'value': '2025-07-01'} |          304475 |