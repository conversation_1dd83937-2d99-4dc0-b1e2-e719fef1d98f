Metadata of all data sources:

````
[
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "journal_entry_line": {
        "table_name": "journal_entry_line",
        "fields": {
          "journal_entry_id": {
            "name": "journal_entry_id",
            "description": "The unique identifier linking this line item to its parent journal entry, ensuring all related lines are grouped under the same accounting transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "16",
              "21",
              "291",
              "1063",
              "and 952 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "A sequential number indicating the specific position of this line within its parent journal entry, distinguishing each line when multiple entries exist for a single journal entry.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 66 more..."
            ]
          },
          "description": {
            "name": "description",
            "description": "A brief text providing context or details about the nature or purpose of this particular debit or credit line within the journal entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "CPP/QPP Employer",
              "EI Employer",
              "Transfer to Hold Co",
              "To clear AR",
              "Red River Valley Mutual MBNA",
              "and 1041 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The financial value associated with this line, representing the debit or credit amount recorded for the corresponding account.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "5",
              "10",
              "17",
              "100",
              "and 2526 more..."
            ]
          },
          "posting_type": {
            "name": "posting_type",
            "description": "Specifies whether the line item represents a debit or a credit transaction, fundamental for double-entry accounting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Debit",
              "Credit"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "References the customer related to this line, identifying client-specific transactions such as accounts receivable; null if not customer-related.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "9",
              "11",
              "14",
              "24",
              "29",
              "and 23 more..."
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "References the vendor associated with this line, used to identify vendor-specific transactions such as accounts payable; null if not vendor-related.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "35",
              "50",
              "184",
              "225",
              "228",
              "259",
              "293",
              "354",
              "357",
              "415"
            ]
          },
          "tax_applicable_on": {
            "name": "tax_applicable_on",
            "description": "Indicates the transaction context ('Sales' or 'Purchase') in which tax is applied for this line, clarifying whether tax pertains to a sale or purchase.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Sales",
              "Purchase"
            ]
          },
          "tax_amount": {
            "name": "tax_amount",
            "description": "The portion of the line amount attributed to tax, included only when tax is relevant to the transaction.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "225",
              "520",
              "600",
              "675",
              "and 43 more..."
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "References a classification or category assigned to this line, supporting segmentation and detailed financial reporting; links to the class table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "References the specific ledger account impacted by this line item, identifying whether the entry affects assets, liabilities, income, or expenses.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "19",
              "30",
              "31",
              "33",
              "34",
              "and 107 more..."
            ]
          },
          "tax_code_id": {
            "name": "tax_code_id",
            "description": "References the tax code applied to this line for reporting and compliance purposes, linking to the tax_code table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "The timestamp recording when this record was last updated or synchronized to the database via Fivetran ETL integration.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:00.175000+00:00",
              "2025-08-06 22:10:00.486000+00:00",
              "2025-08-06 22:10:00.484000+00:00",
              "2025-08-06 22:10:00.240000+00:00",
              "2025-08-06 22:10:00.236000+00:00",
              "and 319 more..."
            ]
          }
        }
      },
      "journal_entry": {
        "table_name": "journal_entry",
        "fields": {
          "id": {
            "name": "id",
            "description": "The primary key string uniquely identifying each journal entry header in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5",
              "8",
              "9",
              "13",
              "15",
              "and 952 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "An optional string referencing the external or accounting document number tied to the journal entry, which may be non-unique or missing.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5",
              "7",
              "11",
              "12",
              "13",
              "and 811 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "A string used to manage version control and detect concurrent updates or synchronization changes to the journal entry record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "8",
              "13",
              "14",
              "16",
              "17",
              "64"
            ]
          },
          "private_note": {
            "name": "private_note",
            "description": "A sparsely populated internal string field used for confidential notes, special categorizations, or instructions relevant to the specific journal entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Opening Balance",
              "Reverse of JE ADJ 1",
              "Reverse of JE 496",
              "Reverse of JE 252"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "The precise timestamp indicating when the journal entry record was first created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2019-10-01 08:50:11+00:00",
              "2019-08-01 08:48:46+00:00",
              "2019-12-01 09:51:57+00:00",
              "2018-03-01 09:29:25+00:00",
              "2019-09-01 08:49:03+00:00",
              "and 944 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "The latest timestamp reflecting when any modification was last saved to the journal entry record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-01 09:29:25+00:00",
              "2022-11-20 18:22:49+00:00",
              "2018-02-24 18:31:52+00:00",
              "2018-03-17 17:59:14+00:00",
              "2018-02-24 19:30:20+00:00",
              "and 950 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The date, without time, representing when the journal entry is considered effective for accounting and reporting purposes.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2021-10-31",
              "2020-10-31",
              "2022-10-31",
              "2017-11-01",
              "and 625 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "The timestamp capturing the most recent Fivetran synchronization event for this journal entry record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:00.326000+00:00",
              "2025-08-06 22:10:00.378000+00:00",
              "2025-08-06 22:10:00.351000+00:00",
              "2025-08-06 22:10:00.266000+00:00",
              "2025-08-06 22:10:00.256000+00:00",
              "and 319 more..."
            ]
          }
        }
      },
      "account": {
        "table_name": "account",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated unique identifier for each account record, serving as the primary key within the chart of accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "12",
              "13",
              "14",
              "15",
              "and 189 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Categorical label denoting the functional group of the account (e.g., 'Accounts Payable'), used for operational grouping and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Current Asset",
              "Other Expense",
              "Other Current Liability",
              "Cost of Goods Sold",
              "Accounts Receivable",
              "Fixed Asset",
              "Credit Card",
              "Bank",
              "Accounts Payable",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "name": {
            "name": "name",
            "description": "User-facing display name for the account, nearly unique and designed for selection in application interfaces.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5785",
              "Insurance",
              "Corporate Tax Receivable",
              "Allowance For Doubtful Accounts",
              "Accounts Receivable",
              "and 187 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "Boolean indicator showing if the account is active (true) or inactive/deleted (false) for filtering and soft deletion.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "classification": {
            "name": "classification",
            "description": "High-level financial category (such as 'Asset', 'Liability', 'Expense', etc.) that determines the account's grouping on financial statements.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Asset",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Fine-grained subtype providing additional specificity within the account_type category, supporting more detailed reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "OfficeGeneralAdministrativeExpenses",
              "OtherMiscellaneousExpense",
              "OtherMiscellaneousServiceCost",
              "OtherCurrentAssets",
              "OtherCurrentLiabilities",
              "and 53 more..."
            ]
          },
          "parent_account_id": {
            "name": "parent_account_id",
            "description": "Reference to the 'id' of the parent account, establishing hierarchical relationships for sub-accounts; null for top-level accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "58",
              "63",
              "66",
              "73",
              "76",
              "109",
              "112",
              "116",
              "120",
              "152",
              "178"
            ]
          },
          "tax_code_id": {
            "name": "tax_code_id",
            "description": "Optional reference to the default tax code for the account, linking to the 'tax_code' table to support tax calculations and compliance.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "7",
              "8"
            ]
          }
        }
      },
      "invoice_line": {
        "table_name": "invoice_line",
        "fields": {
          "invoice_id": {
            "name": "invoice_id",
            "description": "Foreign key referencing the parent invoice for this line item, uniquely identifying the invoice to which the line belongs.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "206",
              "208",
              "1621",
              "2200",
              "2648",
              "and 1682 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "Zero-based position indicating the order of this line item within its invoice, ensuring uniqueness in combination with 'invoice_id'.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Total monetary value for this invoice line, representing the extended or subtotal amount depending on line type.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "2500",
              "2880",
              "3123",
              "4500",
              "6000",
              "and 731 more..."
            ]
          },
          "detail_type": {
            "name": "detail_type",
            "description": "Categorizes the nature of the line item, distinguishing among group lines, subtotals, and individual sales items to determine relevant fields.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "SubTotalLineDetail",
              "SalesItemLineDetail",
              "GroupLineDetail"
            ]
          },
          "sales_item_unit_price": {
            "name": "sales_item_unit_price",
            "description": "Price charged per unit for the sales item on this line, used when the line represents a sales item detail.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "36",
              "100",
              "130",
              "2500",
              "3123",
              "and 598 more..."
            ]
          },
          "sales_item_item_id": {
            "name": "sales_item_item_id",
            "description": "Foreign key referencing the specific item or product sold on this line, linking to the 'item' table when applicable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7"
            ]
          },
          "sales_item_class_id": {
            "name": "sales_item_class_id",
            "description": "Optional foreign key referencing the associated class for this sales item, used for classification in accounting or reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009753",
              "1009755"
            ]
          },
          "sales_item_quantity": {
            "name": "sales_item_quantity",
            "description": "Number of units or quantity of service provided on this line, relevant only for sales item detail lines.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "10",
              "12",
              "20",
              "30",
              "and 87 more..."
            ]
          },
          "sales_item_account_id": {
            "name": "sales_item_account_id",
            "description": "Foreign key linking this sales item line to its related account in the 'account' table for financial categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "10",
              "31",
              "52",
              "75",
              "129",
              "137",
              "141"
            ]
          },
          "sales_item_tax_code_id": {
            "name": "sales_item_tax_code_id",
            "description": "Foreign key indicating the tax code applied to this sales item line, determining tax calculation and treatment.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp of the most recent data synchronization for this record, used for tracking ETL data currency and audit purposes.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:56.628000+00:00",
              "2025-08-06 22:09:56.629000+00:00",
              "2025-08-06 22:09:56.308000+00:00",
              "2025-08-06 22:09:56.309000+00:00",
              "2025-08-06 22:09:56.587000+00:00",
              "and 256 more..."
            ]
          }
        }
      },
      "invoice": {
        "table_name": "invoice",
        "fields": {
          "id": {
            "name": "id",
            "description": "Primary key uniquely identifying each invoice record in the system.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "71",
              "77",
              "128",
              "130",
              "132",
              "and 1682 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "System-assigned invoice number used for client communication and accounting, unique within the invoice table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1003",
              "1006",
              "1035",
              "1036",
              "1037",
              "and 1682 more..."
            ]
          },
          "global_tax_calculation": {
            "name": "global_tax_calculation",
            "description": "Defines the tax application method for the invoice, such as excluding or not applying tax to line items.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "TaxExcluded",
              "NotApplicable"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp capturing when the invoice record was initially created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-30 19:28:22+00:00",
              "2018-03-30 19:23:24+00:00",
              "2018-03-30 19:26:21+00:00",
              "2018-03-30 20:08:44+00:00",
              "2018-03-30 19:25:22+00:00",
              "and 1682 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp of the latest modification made to the invoice record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-04-16 16:01:16+00:00",
              "2024-09-16 15:51:18+00:00",
              "2024-05-24 12:03:19+00:00",
              "2023-10-23 13:36:47+00:00",
              "2021-05-14 13:53:40+00:00",
              "and 1507 more..."
            ]
          },
          "total_tax": {
            "name": "total_tax",
            "description": "The aggregate tax amount calculated for this invoice, based on taxable items and rates.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "125",
              "144",
              "225",
              "300",
              "and 686 more..."
            ]
          },
          "shipping_address_id": {
            "name": "shipping_address_id",
            "description": "Foreign key referencing the shipping address for this invoice in the address table; may be null if shipping is not applicable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "23",
              "26",
              "40",
              "46",
              "and 200 more..."
            ]
          },
          "billing_address_id": {
            "name": "billing_address_id",
            "description": "Foreign key referencing the billing address for this invoice in the address table; may be null if not specified.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "23",
              "26",
              "40",
              "46",
              "and 272 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The grand total billed on the invoice, including all line items and taxes, representing the full amount due.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1050",
              "2625",
              "3024",
              "4725",
              "6300",
              "and 724 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official accounting date assigned to the invoice, used for reporting and ledger entries.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-10-31",
              "2019-01-02",
              "2022-05-22",
              "2022-07-10",
              "2021-11-01",
              "and 867 more..."
            ]
          },
          "sales_term_id": {
            "name": "sales_term_id",
            "description": "Foreign key linking to the payment terms (e.g., Net 30) in the term table, which dictates when payment is due.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "4",
              "6"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Foreign key linking to the customer associated with this invoice, identifying the billed party.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "7",
              "9",
              "10",
              "11",
              "14",
              "and 370 more..."
            ]
          }
        }
      },
      "class": {
        "table_name": "class",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string identifier that serves as the primary key for each business division (class) record in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "The complete, descriptive name for the business division, providing an unambiguous and unique label for reporting and categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "People Culture",
              "Executive",
              "Accounting Operations",
              "Finance",
              "Global Advisory",
              "Information Technology"
            ]
          },
          "name": {
            "name": "name",
            "description": "A concise, human-readable name for the business division, uniquely identifying the class in user interfaces or selection lists.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "People Culture",
              "Executive",
              "Accounting Operations",
              "Finance",
              "Global Advisory",
              "Information Technology"
            ]
          },
          "active": {
            "name": "active",
            "description": "A boolean flag indicating whether the business division is currently operational (TRUE) or inactive/deprecated (FALSE).",
            "dataType": "BOOL",
            "is_unstructured": false
          }
        }
      },
      "customer": {
        "table_name": "customer",
        "fields": {
          "id": {
            "name": "id",
            "description": "A system-generated unique string serving as the primary key for each customer, used to identify and reference individual customer records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "8",
              "9",
              "10",
              "11",
              "and 380 more..."
            ]
          },
          "company_name": {
            "name": "company_name",
            "description": "The formal business or organization name linked to the customer; blank for individuals and nearly unique for companies.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "G3 Canada Ltd",
              "OnBusiness Chartered Professional Accountants",
              "Winnipeg School Division",
              "ARTIS Reit",
              "COFCO International",
              "and 336 more..."
            ]
          },
          "display_name": {
            "name": "display_name",
            "description": "The unique, preferred name shown for the customer in user interfaces and reports, which may be a company or person.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "The Boyd Group",
              "OnBusiness Chartered Professional Accountants",
              "Winnipeg School Division",
              "ARTIS Reit",
              "COFCO International",
              "and 380 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "A boolean indicating whether the customer is currently active (true) or inactive (false) in the system.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "email": {
            "name": "email",
            "description": "The primary email address associated with the customer, which is mostly unique but may be missing or shared in some cases.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "and 197 more..."
            ]
          },
          "phone_number": {
            "name": "phone_number",
            "description": "The primary telephone number for contacting the customer, occasionally shared across multiple customers and sparsely populated.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "************",
              "************",
              "************",
              "************ x1130",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************ x107",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************",
              "************"
            ]
          },
          "default_tax_code_id": {
            "name": "default_tax_code_id",
            "description": "The reference ID for the default tax code assigned to the customer, used to determine tax treatment on transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "3",
              "4"
            ]
          },
          "sales_term_id": {
            "name": "sales_term_id",
            "description": "The reference ID for the sales or payment terms assigned to the customer, linking to the term table for conditions such as payment deadlines.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "6"
            ]
          }
        }
      },
      "tax_code": {
        "table_name": "tax_code",
        "fields": {
          "id": {
            "name": "id",
            "description": "Unique string identifier for each tax code, serving as the table's primary key and reference in related records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11"
            ]
          },
          "active": {
            "name": "active",
            "description": "Boolean flag indicating whether the tax code is currently valid and selectable for transactions.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "description": {
            "name": "description",
            "description": "Detailed textual explanation of the tax code, specifying the type or category of tax it represents (e.g., GST, PST, zero-rated, exempt).",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Tax adjustment",
              "Combined federal and provincial tax (Manitoba)",
              "Provincial sales tax only (Manitoba)",
              "Out of scope",
              "Provincial sales tax (8%) only (Manitoba)",
              "Zero-rated",
              "Federal goods and services tax",
              "Combined federal and provincial tax (8% Manitoba)",
              "Tax-exempt"
            ]
          },
          "name": {
            "name": "name",
            "description": "Short, unique label for the tax code used as a concise reference across the system (e.g., 'GST', 'Exempt', 'GST/PST MB').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "GST/PST MB",
              "PST MB 2013",
              "PST MB Adjustment",
              "PST MB",
              "GST",
              "Zero-rated",
              "Out of Scope",
              "GST/PST MB 2013",
              "GST/HST Adjustment",
              "Exempt"
            ]
          },
          "taxable": {
            "name": "taxable",
            "description": "Boolean value specifying if the tax code results in a taxable transaction (TRUE) or is non-taxable (FALSE).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "tax_group": {
            "name": "tax_group",
            "description": "Boolean flag indicating whether this tax code is part of a combined group of taxes, allowing aggregation with other tax codes.",
            "dataType": "BOOL",
            "is_unstructured": false
          }
        }
      },
      "tax_rate": {
        "table_name": "tax_rate",
        "fields": {
          "id": {
            "name": "id",
            "description": "A string-based primary key that uniquely identifies each tax rate entry within the tax_rate table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11",
              "12",
              "13",
              "14",
              "15",
              "16",
              "17",
              "18",
              "19",
              "20",
              "21"
            ]
          },
          "name": {
            "name": "name",
            "description": "The distinct, human-readable label assigned to each tax rate, such as 'GST' or 'PST (MB)', used for referencing specific tax rates in transactions and reports.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "GST/HST Line 405 Adjustment",
              "MB Balance Adjustment",
              "GST/HST (ITC) ZR",
              "GST/HST ZR",
              "GST EP",
              "NOTAXS",
              "NOTAXP",
              "GST",
              "PST (MB) on purchase",
              "PST (MB) on sales",
              "PST (MB) 2013 on purchase",
              "GST (ITC)",
              "MB Line 3 Adjustment",
              "GST/HST Line 111 Adjustment",
              "GST/HST Line 107 Adjustment",
              "GST/HST Line 104 Adjustment",
              "PST (MB) 2013 on sales",
              "GST ES",
              "MB Line 2 Adjustment",
              "GST/HST Line 110 Adjustment",
              "GST/HST Line 205 Adjustment"
            ]
          },
          "rate_value": {
            "name": "rate_value",
            "description": "The percentage value of the tax rate, stored as a numeric value (e.g., 5 for 5%), representing the standard rate applied to transactions; may be null if the rate is not directly set.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "5",
              "7",
              "8"
            ]
          },
          "description": {
            "name": "description",
            "description": "Additional descriptive text providing context or details about the tax rate's application or purpose, aiding user understanding beyond the tax rate's name.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "GST",
              "PST (MB)",
              "No Tax"
            ]
          },
          "tax_agency_id": {
            "name": "tax_agency_id",
            "description": "Foreign key referencing the 'id' of the tax_agency table, identifying the government or administrative agency responsible for the given tax rate.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3"
            ]
          }
        }
      }
    },
    "reasoning": "  \n  \n**Reasoning:**  \nThe user requests metadata for the QuickBooks_Reports.quickbooks__general_ledger table, but this table is not present in your provided metadata. However, the original prompt asks for a focus on revenue calculation and columns like adjusted_amount, transaction_type, account_class, and revenue recognition. The closest matches in your metadata for these requirements are the following tables: journal_entry_line (for line detail by transaction), journal_entry (header), account (for account details and class), invoice_line (for revenue line items), and possibly invoice (header for revenue transactions). To provide broader schema context and relationships, relevant tables and primary/foreign keys are also listed.  \n  \n  \n**Table Columns:**   \n   - table: journal_entry_line  \n      columns: journal_entry_id, index, amount, posting_type, account_id, description, customer_id, vendor_id, class_id, tax_code_id, tax_applicable_on, tax_amount, _fivetran_synced  \n   - table: journal_entry  \n      columns: id, doc_number, sync_token, private_note, created_at, updated_at, transaction_date, _fivetran_synced  \n   - table: account  \n      columns: id, name, account_type, classification, account_sub_type, active, parent_account_id, tax_code_id  \n   - table: invoice_line  \n      columns: invoice_id, index, amount, detail_type, sales_item_unit_price, sales_item_item_id, sales_item_account_id, sales_item_tax_code_id, sales_item_quantity, sales_item_class_id, _fivetran_synced  \n   - table: invoice  \n      columns: id, doc_number, customer_id, total_amount, total_tax, transaction_date, created_at, updated_at, sales_term_id, billing_address_id, shipping_address_id, global_tax_calculation  \n   - table: class  \n      columns: id, name, fully_qualified_name, active  \n   - table: customer  \n      columns: id, display_name, company_name, email, phone_number, active, sales_term_id, default_tax_code_id  \n   - table: tax_code  \n      columns: id, name, description, taxable, tax_group, active  \n   - table: tax_rate  \n      columns: id, name, rate_value, description, tax_agency_id  \n  \n  \n**ER Diagram:**  \nThe ER diagram has been provided above in JSON format and shows how journal_entry_line relates to journal_entry (via journal_entry_id), account (via account_id), customer (via customer_id), vendor (via vendor_id), class (via class_id), and tax_code (via tax_code_id). Invoice_line relates to invoice (via invoice_id), account (via sales_item_account_id), item (via sales_item_item_id), class (via sales_item_class_id), and tax_code (via sales_item_tax_code_id). The account table provides classification and type for financial reporting.  \n",
    "other_table_columns": {
      "item": [
        "id",
        "fully_qualified_name",
        "name",
        "sync_token",
        "type",
        "sales_tax_included",
        "created_at",
        "updated_at",
        "income_account_id",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [
        "invoice_id",
        "index",
        "line_num",
        "description",
        "amount",
        "unit_price",
        "item_id",
        "class_id",
        "quantity",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [
        "id",
        "sync_token",
        "balance",
        "doc_number",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "due_date",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "estimate_line": [],
      "journal_entry_tax_line": [
        "journal_entry_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "term": [
        "id",
        "name",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [
        "purchase_id",
        "index",
        "amount",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "credit_card_payment_txn": [
        "id",
        "amount",
        "currency_id",
        "created_at",
        "updated_at",
        "transaction_date",
        "bank_account_id",
        "credit_card_account_id",
        "_fivetran_synced"
      ],
      "tax_code": [
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_type_applicable",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [
        "bill_id",
        "index",
        "bill_payment_id",
        "_fivetran_synced"
      ],
      "transfer": [
        "id",
        "amount",
        "private_note",
        "created_at",
        "updated_at",
        "currency_id",
        "transaction_date",
        "to_account_id",
        "from_account_id",
        "_fivetran_synced"
      ],
      "vendor": [
        "id",
        "family_name",
        "given_name",
        "company_name",
        "active",
        "balance",
        "display_name",
        "print_on_check_name",
        "middle_name",
        "sync_token",
        "billing_address_id",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "special_tax_type",
        "display_type",
        "effective_tax_rate",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [
        "invoice_id",
        "index",
        "payment_id",
        "_fivetran_synced"
      ],
      "deposit_line": [
        "deposit_id",
        "index",
        "description",
        "detail_type",
        "amount",
        "deposit_tax_applicable_on",
        "deposit_tax_code_id",
        "deposit_class_id",
        "deposit_account_id",
        "deposit_customer_id",
        "_fivetran_synced"
      ],
      "tax_agency": [
        "id",
        "display_name",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [
        "id",
        "pay_type",
        "doc_number",
        "sync_token",
        "currency_id",
        "check_print_status",
        "check_bank_account_id",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "account": [
        "currency_id",
        "description",
        "fully_qualified_name",
        "sub_account",
        "sync_token",
        "created_at",
        "updated_at",
        "balance_with_sub_accounts",
        "balance",
        "account_number",
        "_fivetran_synced"
      ],
      "address": [
        "id",
        "line_1",
        "line_2",
        "line_3",
        "line_4",
        "city",
        "postal_code",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [
        "id",
        "email_status",
        "doc_number",
        "sync_token",
        "global_tax_calculation",
        "shipping_address_id",
        "billing_address_id",
        "bill_email",
        "customer_memo",
        "total_tax",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "class": [
        "sync_token",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bill_payment_line": [
        "bill_payment_id",
        "index",
        "amount",
        "bill_id",
        "journal_entry_id",
        "_fivetran_synced"
      ],
      "payment_line": [
        "payment_id",
        "index",
        "amount",
        "journal_entry_id",
        "credit_memo_id",
        "invoice_id",
        "_fivetran_synced"
      ],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "bill_line": [
        "bill_id",
        "index",
        "description",
        "amount",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "_fivetran_synced"
      ],
      "payment": [
        "id",
        "sync_token",
        "created_at",
        "updated_at",
        "reference_number",
        "unapplied_amount",
        "total_amount",
        "transaction_date",
        "payment_method_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "email_status",
        "print_status",
        "balance",
        "sync_token",
        "delivery_time",
        "billing_email",
        "customer_memo",
        "due_date",
        "_fivetran_synced"
      ],
      "purchase": [
        "id",
        "sync_token",
        "credit",
        "doc_number",
        "payment_type",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "customer_id",
        "vendor_id",
        "remit_to_address_id",
        "total_tax",
        "total_amount",
        "transaction_date",
        "account_id",
        "payment_method_id",
        "_fivetran_synced"
      ],
      "customer": [
        "family_name",
        "fully_qualified_name",
        "given_name",
        "print_on_check_name",
        "title",
        "middle_name",
        "sync_token",
        "balance_with_jobs",
        "balance",
        "taxable",
        "created_at",
        "updated_at",
        "website",
        "mobile_number",
        "shipping_address_id",
        "bill_address_id",
        "_fivetran_synced"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "id",
        "line_num",
        "description",
        "bundle_quantity"
      ],
      "credit_memo_line": [
        "credit_memo_id",
        "index",
        "description",
        "amount",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_account_id",
        "_fivetran_synced"
      ],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "payment_method": [
        "id",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [
        "purchase_id",
        "index",
        "amount",
        "description",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "account_expense_customer_id",
        "_fivetran_synced"
      ],
      "deposit": [
        "id",
        "sync_token",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "account_id",
        "_fivetran_synced"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Reports",
    "selected_table_column_metadata": {
      "quickbooks__general_ledger": {
        "table_name": "quickbooks__general_ledger",
        "fields": {
          "unique_id": {
            "name": "unique_id",
            "description": "A system-generated primary key that uniquely identifies each individual general ledger line item in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "177761637a1e9da971584f556c12fe54",
              "6121a0bf392766b81cff0d01569de02e",
              "aa7f2f904eb74faf0a38f71b6ce1a147",
              "7701a8814098f05c67dd1908ad4c5cb8",
              "5516b1e25a4d65c8d6d52f614f7eb224",
              "and 25457 more..."
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "An identifier linking this ledger entry to a specific accounting transaction, grouping all related line items under the same transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "16",
              "21",
              "291",
              "1063",
              "and 6559 more..."
            ]
          },
          "transaction_index": {
            "name": "transaction_index",
            "description": "The sequential position of this line item within its associated transaction, used to preserve the original order of entries.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 66 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The effective date on which the accounting transaction was recognized in the ledger.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2017-10-31",
              "2020-10-31",
              "2017-11-30",
              "and 1932 more..."
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "A reference to the customer involved in the transaction, present only if the ledger entry pertains to a specific customer.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "7",
              "9",
              "10",
              "11",
              "14",
              "and 379 more..."
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "A reference to the vendor involved in the transaction, included only for entries directly associated with a vendor.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "4",
              "5",
              "6",
              "35",
              "and 248 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The posted monetary value for this ledger entry, which may be positive or negative based on whether it represents a debit or credit.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "375",
              "500",
              "2500",
              "5000",
              "and 5834 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique reference identifier to the specific chart of accounts entry affected by this ledger line item.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "19",
              "30",
              "31",
              "55",
              "58",
              "and 123 more..."
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "An optional code used to assign additional categorization, such as department or project, to the ledger entry for enhanced reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "The formal account number from the chart of accounts associated with this ledger line item.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1200",
              "1210",
              "1220",
              "1330",
              "and 106 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The descriptive name of the specific account impacted by this ledger entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Chequing",
              "Accounts Receivable (A/R)",
              "Corporate Tax Receivable",
              "Advances & Loans",
              "Accounts Payable (A/P)",
              "and 121 more..."
            ]
          },
          "is_sub_account": {
            "name": "is_sub_account",
            "description": "A boolean value indicating if the impacted account is a sub-account (true) or a parent/main account (false) in the chart of accounts.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "parent_account_number": {
            "name": "parent_account_number",
            "description": "The account number of the parent account, provided only when the affected account is a sub-account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1055",
              "1060",
              "1200",
              "1210",
              "1220",
              "and 52 more..."
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The descriptive name of the parent account, provided when the affected account is a sub-account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Chequing",
              "Accounts Receivable (A/R)",
              "Corporate Tax Receivable",
              "Advances & Loans",
              "Office/General Administrative Expenses",
              "and 65 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "The primary accounting category of the affected account, such as 'Expense', 'Asset', or 'Liability'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable",
              "Expense",
              "Bank",
              "Accounts Payable",
              "Income",
              "Other Current Asset",
              "Other Current Liability",
              "Credit Card",
              "Other Expense",
              "Fixed Asset",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "A more specific classification within the account type, detailing the nature of the account (e.g., 'OfficeGeneralAdministrativeExpenses').",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "AccountsReceivable",
              "Checking",
              "AccountsPayable",
              "OfficeGeneralAdministrativeExpenses",
              "ServiceFeeIncome",
              "and 39 more..."
            ]
          },
          "financial_statement_helper": {
            "name": "financial_statement_helper",
            "description": "Indicates whether the account is primarily reported on the 'balance_sheet' or 'income_statement', aiding in financial statement categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "balance_sheet",
              "income_statement"
            ]
          },
          "account_current_balance": {
            "name": "account_current_balance",
            "description": "The balance of the account at the time of this ledger entry, reflecting its updated value after the transaction.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "300",
              "20000",
              "177198",
              "709000",
              "and 31 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A high-level grouping of the account for financial reporting, such as 'Revenue', 'Asset', 'Expense', 'Liability', or 'Equity'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Expense",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Specifies whether this ledger line is a debit or a credit, defining its effect on the account balance.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "transaction_source": {
            "name": "transaction_source",
            "description": "Describes the origin or method by which the transaction was created in the accounting system (e.g., 'bill').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "invoice",
              "payment",
              "journal_entry",
              "bill",
              "purchase",
              "bill payment",
              "deposit",
              "credit card payment",
              "credit_memo",
              "transfer",
              "vendor_credit"
            ]
          },
          "account_transaction_type": {
            "name": "account_transaction_type",
            "description": "Indicates the nature of the transaction's impact on the account, specifying if it is a debit or credit from the account's perspective.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "The timestamp recording when this ledger entry was first created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-02-24 17:52:31+00:00",
              "2021-02-23 23:59:46+00:00",
              "2018-04-15 14:47:54+00:00",
              "2018-11-24 21:28:52+00:00",
              "2018-02-24 19:14:04+00:00",
              "and 6421 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "The timestamp indicating the most recent update or modification to this ledger entry.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2020-10-19 19:12:29+00:00",
              "2019-04-06 18:57:10+00:00",
              "2018-09-01 18:25:22+00:00",
              "2018-06-24 15:42:12+00:00",
              "2019-04-08 21:40:04+00:00",
              "and 4384 more..."
            ]
          },
          "adjusted_amount": {
            "name": "adjusted_amount",
            "description": "The monetary value for this entry after any corrections or adjustments have been applied, which may differ from the original amount.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2500",
              "3123",
              "8000",
              "8400",
              "and 7488 more..."
            ]
          },
          "adjusted_converted_amount": {
            "name": "adjusted_converted_amount",
            "description": "The entry\u2019s monetary value after adjustments, expressed in a standardized or reporting currency for cross-currency consistency.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2500",
              "3123",
              "8000",
              "8400",
              "and 7488 more..."
            ]
          },
          "running_balance": {
            "name": "running_balance",
            "description": "The cumulative account balance immediately after this transaction in the account\u2019s original currency, showing the sequential change over time.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "200",
              "160536.99",
              "28349.45",
              "-212316.07",
              "and 19558 more..."
            ]
          },
          "running_converted_balance": {
            "name": "running_converted_balance",
            "description": "The cumulative account balance after this transaction, expressed in the reporting or base currency, incorporating currency conversions and adjustments.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "200",
              "160536.99",
              "28349.45",
              "-212316.07",
              "and 19558 more..."
            ]
          }
        }
      },
      "quickbooks__balance_sheet": {
        "table_name": "quickbooks__balance_sheet",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The exact date representing when the balance sheet snapshot was taken, typically marking the end of a reporting period.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2022-06-01",
              "2024-09-01",
              "2024-06-01",
              "2024-12-01",
              "2018-04-01",
              "and 96 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the accounting period covered by this balance sheet entry, indicating when the reporting interval begins.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2022-06-01",
              "2024-09-01",
              "2024-06-01",
              "2024-12-01",
              "2018-04-01",
              "and 96 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The ending date of the accounting period covered by this balance sheet entry, indicating when the reporting interval concludes.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2022-06-30",
              "2024-09-30",
              "2024-06-30",
              "2024-12-31",
              "2018-04-30",
              "and 96 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A top-level category placing the account into one of the three main balance sheet groups: Asset, Liability, or Equity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Liability",
              "Equity"
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "A system-generated identifier uniquely representing the account_class for internal mapping and reporting, not always populated.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "is_sub_account": {
            "name": "is_sub_account",
            "description": "Indicates if the account operates as a sub-account (true) under a parent account or as an independent top-level account (false).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "parent_account_number": {
            "name": "parent_account_number",
            "description": "The account number assigned to the parent account when this account is a sub-account; blank for top-level accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1200",
              "1210",
              "1220",
              "1332",
              "and 35 more..."
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The name of the parent account if the current account is nested under another, aiding in hierarchical account structures.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Accounts Payable (A/P)",
              "Accounts Receivable (A/R)",
              "Chequing",
              "TD Visa-3534",
              "Due To/From Dan Lambert",
              "and 42 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "The main accounting classification of the account (such as 'Bank', 'Equity', or 'Fixed Asset'), used to group accounts by financial function.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Credit Card",
              "Other Current Asset",
              "Accounts Receivable",
              "Other Current Liability",
              "Fixed Asset",
              "Bank",
              "Accounts Payable",
              "Equity",
              "Other Asset"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "A detailed breakdown of the account's purpose within its type, providing additional specificity (e.g., 'Inventory' under 'Other Current Asset').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "CreditCard",
              "AccountsReceivable",
              "OtherCurrentLiabilities",
              "OtherCurrentAssets",
              "AccountsPayable",
              "Checking",
              "FurnitureAndFixtures",
              "OtherFixedAssets",
              "RetainedEarnings",
              "AccumulatedAmortization",
              "OwnersEquity",
              "OrganizationalCosts",
              "UndepositedFunds",
              "OpeningBalanceEquity",
              "Inventory",
              "Savings",
              "Investment_Other",
              "MachineryAndEquipment",
              "ShareCapital",
              "GlobalTaxPayable",
              "GlobalTaxSuspense",
              "CashOnHand"
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "The unique code or identifier assigned to the account in the chart of accounts, used for reference and sorting; may be blank.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1200",
              "1202",
              "1210",
              "1220",
              "and 38 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A persistent, system-generated unique identifier for the account, essential for linking balances and maintaining data consistency across periods.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "55",
              "58",
              "59",
              "60",
              "106",
              "and 45 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The official descriptive name of the account from the chart of accounts, used for clear identification in reports and interfaces.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Accounts Payable (A/P)",
              "Chequing",
              "TD Visa-3534",
              "TD Visa-7463",
              "Due To/From Dan Lambert",
              "and 45 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The raw balance for the account during the period, expressed in the company's base currency and reflecting the account's financial position.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1208 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "The account balance translated into another reporting currency based on relevant exchange rates; mirrors 'amount' if conversion is unnecessary.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1208 more..."
            ]
          },
          "account_ordinal": {
            "name": "account_ordinal",
            "description": "A numeric value specifying the display or processing sequence of the account on the balance sheet, ensuring consistent ordering.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3"
            ]
          }
        }
      },
      "quickbooks__ap_ar_enhanced": {
        "table_name": "quickbooks__ap_ar_enhanced",
        "fields": {
          "transaction_type": {
            "name": "transaction_type",
            "description": "Classifies the financial transaction as either an 'invoice' for accounts receivable or a 'bill' for accounts payable, distinguishing between customer and vendor transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "invoice",
              "bill"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique system-generated identifier for each invoice or bill, serving as the table's primary key for tracking individual transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "556",
              "594",
              "880",
              "966",
              "1010",
              "and 2471 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "The external or reference document number assigned to the invoice or bill, used for reconciliation and client/vendor correspondence; may be absent or occasionally duplicated.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Starbucks",
              "QB",
              "Staples",
              "Impark",
              "Shaw",
              "and 2028 more..."
            ]
          },
          "transaction_with": {
            "name": "transaction_with",
            "description": "Indicates whether the transaction is with a 'customer' (receivable) or 'vendor' (payable), enabling segmentation by counterparty type.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "customer",
              "vendor"
            ]
          },
          "customer_vendor_name": {
            "name": "customer_vendor_name",
            "description": "The full name of the customer or vendor involved in the transaction, identifying the specific business or individual counterpart.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "TD Visa-1077",
              "Price Industries",
              "G3 Canada Ltd",
              "Adama Canada Ltd",
              "Canada Life",
              "and 433 more..."
            ]
          },
          "customer_vendor_balance": {
            "name": "customer_vendor_balance",
            "description": "The running balance owed by or to the named customer or vendor at the time of the transaction, reflecting their overall account status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2",
              "10500",
              "15750",
              "21000",
              "and 52 more..."
            ]
          },
          "customer_vendor_address_city": {
            "name": "customer_vendor_address_city",
            "description": "The city from the address of the customer or vendor, provided when available, for geographic reporting and analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Winnipeg",
              "West St. Paul",
              "Toronto",
              "Oak Bluff",
              "Montreal",
              "Blumenort",
              "Rosser",
              "Altona",
              "Rosenort",
              "Headingley",
              "Springfield",
              "Morden",
              "Grande Pointe",
              "Sunnyside",
              "Morris",
              "Regina",
              "Winkler",
              "Arborg",
              "Saskatoon",
              "Steinbach",
              "West Saint Paul"
            ]
          },
          "customer_vendor_address_line": {
            "name": "customer_vendor_address_line",
            "description": "The street address or main address line for the customer or vendor, included in a minority of records for detailed location reference.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Edison Properties401-10 Fort Street",
              "Johanna ChipmanControlled Environments Limited",
              "Laura Guth-MazolleckBird Construction Group",
              "Kathy MacDonaldControlled Environments Limited",
              "G3 Canada Ltd200 Portage Avenue",
              "and 39 more..."
            ]
          },
          "customer_vendor_website": {
            "name": "customer_vendor_website",
            "description": "The website URL associated with the customer or vendor, if known, for contact or business verification purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "http://www.edisonproperties.ca",
              "http://www.mbtechaccelerator.com"
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The full monetary value of the invoice or bill in its original transaction currency, representing the total amount charged or billed.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "756",
              "2100",
              "2625",
              "3024",
              "4725",
              "and 1292 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "The transaction amount converted from the original currency to a standard base currency, supporting consolidated financial reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "756",
              "2100",
              "2625",
              "3024",
              "4725",
              "and 1292 more..."
            ]
          },
          "current_balance": {
            "name": "current_balance",
            "description": "The outstanding unpaid amount on the invoice or bill at the reporting date, representing what is still owed.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "8400",
              "8820",
              "10500",
              "11088",
              "and 81 more..."
            ]
          },
          "due_date": {
            "name": "due_date",
            "description": "The date when payment is contractually due for the invoice or bill, central to overdue and cash flow analysis.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-11-30",
              "2019-02-01",
              "2022-06-01",
              "2018-07-18",
              "2018-11-01",
              "and 1180 more..."
            ]
          },
          "is_overdue": {
            "name": "is_overdue",
            "description": "A boolean flag indicating if the invoice or bill remains unpaid past its due date as of the current reporting period.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "days_overdue": {
            "name": "days_overdue",
            "description": "The number of days the transaction has been overdue, calculated from the due date to the reporting date; zero if not overdue.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "8",
              "11",
              "14",
              "19",
              "and 15 more..."
            ]
          },
          "initial_payment_date": {
            "name": "initial_payment_date",
            "description": "The date on which the first payment was applied toward this invoice or bill, marking the beginning of payment activity.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-01",
              "2018-07-30",
              "2018-11-28",
              "2018-04-30",
              "2018-06-26",
              "and 1015 more..."
            ]
          },
          "recent_payment_date": {
            "name": "recent_payment_date",
            "description": "The date of the latest payment received or made for this transaction, capturing the most recent payment action.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-11-01",
              "2018-11-28",
              "2018-07-30",
              "2018-04-30",
              "2018-06-26",
              "and 1018 more..."
            ]
          },
          "total_current_payment": {
            "name": "total_current_payment",
            "description": "The sum of all payments made toward the transaction, expressed in the original transaction currency, for payment tracking.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "756",
              "2100",
              "2625",
              "3024",
              "and 1268 more..."
            ]
          },
          "total_current_converted_payment": {
            "name": "total_current_converted_payment",
            "description": "The total payments received or made for the transaction, converted to the base currency for unified payment reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "756",
              "2100",
              "2625",
              "3024",
              "and 1268 more..."
            ]
          }
        }
      },
      "quickbooks__expenses_sales_enhanced": {
        "table_name": "quickbooks__expenses_sales_enhanced",
        "fields": {
          "transaction_source": {
            "name": "transaction_source",
            "description": "Categorizes each transaction line as either an expense or a sale, enabling high-level separation for reporting and analytics.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "expense",
              "sales"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "System-generated identifier that groups all line items belonging to a single transaction; may repeat across multiple rows for multi-line transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "1158",
              "4723",
              "4918",
              "5445",
              "and 3938 more..."
            ]
          },
          "transaction_line_id": {
            "name": "transaction_line_id",
            "description": "Unique line item number within a specific transaction, used with 'transaction_id' to pinpoint individual transaction lines.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 37 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "User-facing reference or document number assigned to the transaction, such as an invoice or bill number; not guaranteed to be unique or always present.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "SVCCHRG",
              "ADJ448",
              "ADJ447",
              "ADJ445",
              "and 2487 more..."
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Enumerated value specifying the detailed classification of the transaction, such as 'invoice', 'bill', or 'journal_entry'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "journal_entry",
              "invoice",
              "bill",
              "purchase",
              "credit_memo",
              "deposit",
              "vendor_credit"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The official date on which the transaction was recorded or occurred, used for period-based financial tracking.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2025-03-01",
              "2025-02-01",
              "2025-07-19",
              "and 1544 more..."
            ]
          },
          "item_id": {
            "name": "item_id",
            "description": "Identifier for the product or service item detailed in the line, present mainly for transactions involving specific goods or services.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3"
            ]
          },
          "item_quantity": {
            "name": "item_quantity",
            "description": "The count of items or units for the product or service on this line, primarily relevant for itemized sales or purchases.",
            "dataType": "FLOAT64",
            "is_unstructured": false,
            "Subset of values": [
              "1.0",
              "80.0",
              "55.0",
              "44.0",
              "10.0",
              "and 87 more..."
            ]
          },
          "item_unit_price": {
            "name": "item_unit_price",
            "description": "The price per unit for the item on this transaction line, providing the basis for line-level calculations when items are specified.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "36",
              "100",
              "130",
              "2500",
              "3123",
              "and 578 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Unique identifier for the general ledger (GL) account linked to this line, used for accounting and reporting purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "33",
              "and 67 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "Descriptive name of the GL account associated with the transaction line, aiding in categorization of financial activity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Perm Revenue",
              "Office Expenses",
              "Temp Revenue",
              "Meals and entertainment",
              "Wages & Salaries",
              "and 65 more..."
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Detailed sub-category of the GL account, offering finer granularity for accounting and expense tracking.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "OfficeGeneralAdministrativeExpenses",
              "ServiceFeeIncome",
              "PayrollExpenses",
              "EntertainmentMeals",
              "CostOfLabor",
              "BankCharges",
              "OtherMiscellaneousServiceCost",
              "AdvertisingPromotional",
              "OtherMiscellaneousExpense",
              "Travel",
              "Auto",
              "LegalProfessionalFees",
              "TaxesPaid",
              "Insurance",
              "TravelMeals",
              "InterestPaid",
              "BadDebts",
              "Amortization"
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "Optional code representing the business segment, department, or class associated with this transaction line for internal tracking.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Unique identifier for the customer related to this transaction line, populated mainly for sales or billable expense entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "7",
              "9",
              "10",
              "11",
              "14",
              "and 329 more..."
            ]
          },
          "customer_name": {
            "name": "customer_name",
            "description": "Name of the customer connected to the transaction line, typically found on sales-related records and may not be unique.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "G3 Canada Ltd",
              "Price Industries",
              "Adama Canada Ltd",
              "Arctic Glacier",
              "The Boyd Group",
              "and 326 more..."
            ]
          },
          "customer_website": {
            "name": "customer_website",
            "description": "Web address of the customer, included when available to provide supplementary context for customer-linked lines.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "http://www.edisonproperties.ca",
              "http://www.mbtechaccelerator.com"
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "Unique identifier for the vendor or supplier associated with the line item, particularly relevant to expense or purchase transactions.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "4",
              "35",
              "57",
              "78",
              "and 236 more..."
            ]
          },
          "vendor_name": {
            "name": "vendor_name",
            "description": "Name of the vendor or supplier linked to the line, present mainly for expense-related records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "TD Visa-1077",
              "Canada Life",
              "Harvard Property Management",
              "Brittani Lutz",
              "Vuppala Info Systems",
              "and 236 more..."
            ]
          },
          "billable_status": {
            "name": "billable_status",
            "description": "Indicates if the line is billable to a customer; always set to 'NotBillable' in this dataset.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "NotBillable"
            ]
          },
          "description": {
            "name": "description",
            "description": "Optional free-text field capturing additional notes or details about the transaction line, such as merchant names or explanations.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "CPP/QPP Employer",
              "EI Employer",
              "Impark",
              "Wages",
              "August Rent",
              "and 3242 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Monetary value of the line item in the transaction's original currency, reflecting the specific entry before conversion.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Monetary value of the line item converted to a standard reporting currency, supporting consolidated financial analysis.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "15",
              "25",
              "100",
              "125",
              "175",
              "and 3784 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Sum of all line items for the transaction in the original currency, representing the transaction's gross value.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "100",
              "375",
              "756",
              "2100",
              "and 1890 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "Total amount for the entire transaction converted into the reporting currency, enabling cross-currency comparisons.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "100",
              "375",
              "756",
              "2100",
              "and 1890 more..."
            ]
          }
        }
      },
      "quickbooks__profit_and_loss": {
        "table_name": "quickbooks__profit_and_loss",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The exact date associated with this financial record, reflecting when the transaction or summary was recorded in the accounting period.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-01",
              "2018-03-01",
              "2018-04-01",
              "2018-05-01",
              "2018-07-01",
              "and 96 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the accounting period this record summarizes, marking the beginning of the reporting interval (e.g., month or quarter).",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-01",
              "2018-03-01",
              "2018-04-01",
              "2018-05-01",
              "2018-07-01",
              "and 96 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The ending date of the accounting period this record summarizes, marking the close of the reporting interval.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-12-31",
              "2018-03-31",
              "2018-04-30",
              "2018-05-31",
              "2018-07-31",
              "and 96 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "Indicates whether the transaction relates to revenue or expense, providing a primary split for profit and loss categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Revenue"
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "A business-defined string identifier for categorizing the transaction or account, used for internal segmentation or grouping; may be absent for some records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "is_sub_account": {
            "name": "is_sub_account",
            "description": "Boolean flag indicating if the account is a sub-account (True) or a primary account (False), clarifying account hierarchy for reporting.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "parent_account_number": {
            "name": "parent_account_number",
            "description": "References the account number of the immediate parent, establishing the parent-child relationship for sub-accounts; may be empty if not applicable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "4000",
              "4030",
              "4040",
              "4045",
              "4050",
              "5129",
              "5400",
              "5500",
              "5600",
              "5630",
              "5634",
              "5636",
              "5800",
              "5815",
              "5850",
              "6000",
              "6100"
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The descriptive name of the parent account, providing context when the account is part of a hierarchical structure.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Office/General Administrative Expenses",
              "Payroll Expenses",
              "Office/General Administrative Expenses:Computer Expenses",
              "Home Office",
              "Temp Expenses",
              "Sales",
              "Auto",
              "Office/General Administrative Expenses:Computer Expenses:Software",
              "Education & Upgrade",
              "Conference Costs",
              "Meals & Entertainment-100%",
              "Other Income",
              "Website",
              "Commissions Expense",
              "IT-Information Technician Expense",
              "Unapplied Cash Payment Income",
              "Property Tax",
              "Office Space Income",
              "Canada Emergency Wage Subsidy",
              "Foregivenss from CEBA Loan",
              "Uncategorized Expense",
              "Amortization",
              "Interest earned"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Specifies the high-level financial category of the account, such as 'Expense', 'Income', 'Other Expense', or 'Other Income', aligning with standard financial statement classifications.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Expense",
              "Other Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides detailed categorization within the account type, describing the specific nature or purpose of the account (e.g., 'OfficeGeneralAdministrativeExpenses').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "OfficeGeneralAdministrativeExpenses",
              "PayrollExpenses",
              "OtherMiscellaneousServiceCost",
              "AdvertisingPromotional",
              "OtherMiscellaneousExpense",
              "ServiceFeeIncome",
              "CostOfLabor",
              "Auto",
              "EntertainmentMeals",
              "BankCharges",
              "OtherPrimaryIncome",
              "Travel",
              "LegalProfessionalFees",
              "BadDebts",
              "UnappliedCashPaymentIncome",
              "Amortization",
              "TaxesPaid",
              "TravelMeals",
              "InterestPaid",
              "InterestEarned",
              "DiscountsRefundsGiven",
              "Insurance"
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "The unique string identifier for the account within the chart of accounts, used for referencing accounts; can be missing in some cases.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "4005",
              "5190",
              "5410",
              "5420",
              "5430",
              "and 64 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A system-generated unique identifier for the account, ensuring precise account reference within the accounting software.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "32",
              "and 74 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The human-readable name of the account, such as 'Marketing' or 'Office Space Income', used for clear identification in reports.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Wages & Salaries",
              "EI Expense",
              "Employee Benefits",
              "Temp Payroll",
              "CPP Expense",
              "and 72 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The original transaction amount recorded for this entry, which may be positive or negative depending on the financial activity.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "27",
              "100",
              "150",
              "and 2135 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "The transaction amount expressed in a standardized or reporting currency, enabling consolidated analysis across multi-currency records.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "27",
              "100",
              "150",
              "and 2135 more..."
            ]
          },
          "account_ordinal": {
            "name": "account_ordinal",
            "description": "An integer representing the account's hierarchical level or display order within the account structure, distinguishing main from sub-accounts.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2"
            ]
          }
        }
      },
      "quickbooks__general_ledger_by_period": {
        "table_name": "quickbooks__general_ledger_by_period",
        "fields": {
          "account_id": {
            "name": "account_id",
            "description": "Unique internal identifier for the general ledger account, used to link records to specific accounts in the chart of accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "17",
              "19",
              "30",
              "31",
              "33",
              "and 124 more..."
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "Official alphanumeric code assigned to the account, used for referencing and ordering within the chart of accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1332",
              "2200",
              "2215",
              "5190",
              "and 107 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "Descriptive label of the account, such as 'Office Space Income', providing a human-readable account identifier.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Employee Benefits",
              "Accounts Payable (A/P)",
              "CPP Expense",
              "EI Expense",
              "Rent",
              "and 122 more..."
            ]
          },
          "is_sub_account": {
            "name": "is_sub_account",
            "description": "Boolean flag indicating if the account is a sub-account (True) or a top-level/parent account (False) within the ledger hierarchy.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "parent_account_number": {
            "name": "parent_account_number",
            "description": "Account number of the immediate parent account, present only if the account is classified as a sub-account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1105",
              "1200",
              "1210",
              "1220",
              "and 52 more..."
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "Name of the parent account for sub-accounts, providing a human-readable reference for hierarchical relationships.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Office/General Administrative Expenses",
              "Payroll Expenses",
              "Home Office",
              "Office/General Administrative Expenses:Computer Expenses",
              "Temp Expenses",
              "and 65 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "General accounting classification of the account, such as 'Income', 'Expense', 'Asset', etc., based on industry standards.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Credit Card",
              "Income",
              "Other Current Asset",
              "Other Current Liability",
              "Accounts Receivable",
              "Other Expense",
              "Fixed Asset",
              "Accounts Payable",
              "Bank",
              "Equity",
              "Other Asset",
              "Other Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Specific subcategory within the main account type, offering further granularity (e.g., 'ServiceFeeIncome' under 'Income').",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "OfficeGeneralAdministrativeExpenses",
              "PayrollExpenses",
              "OtherMiscellaneousServiceCost",
              "CreditCard",
              "AccountsReceivable",
              "and 39 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "High-level financial grouping of the account, such as 'Expense', 'Revenue', 'Asset', 'Liability', or 'Equity', for reporting purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Asset",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "ID representing an associated business class, department, or segment for the account, supporting departmental or segmented reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "financial_statement_helper": {
            "name": "financial_statement_helper",
            "description": "Indicates whether the account appears on the 'income_statement' or 'balance_sheet' for financial reporting alignment.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "income_statement",
              "balance_sheet"
            ]
          },
          "date_year": {
            "name": "date_year",
            "description": "Fiscal year corresponding to the record, formatted as YYYY-01-01, to group data by annual periods.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2022-01-01",
              "2020-01-01",
              "2024-01-01",
              "2018-01-01",
              "2021-01-01",
              "and 4 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "Date marking the start of the accounting period covered by this record.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-08-01",
              "2018-10-01",
              "2018-12-01",
              "2018-01-01",
              "2018-11-01",
              "and 96 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "Date marking the end of the accounting period covered by this record.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2017-08-31",
              "2018-10-31",
              "2018-12-31",
              "2018-01-31",
              "2018-11-30",
              "and 96 more..."
            ]
          },
          "period_net_change": {
            "name": "period_net_change",
            "description": "Net change in the account balance during the period, reflecting the sum of debits and credits in the account's native currency.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "175",
              "250",
              "375",
              "and 3190 more..."
            ]
          },
          "period_beginning_balance": {
            "name": "period_beginning_balance",
            "description": "Account balance at the opening of the period, in the account's original currency.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1203 more..."
            ]
          },
          "period_ending_balance": {
            "name": "period_ending_balance",
            "description": "Account balance at the close of the period, in the account's original currency.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1208 more..."
            ]
          },
          "period_net_converted_change": {
            "name": "period_net_converted_change",
            "description": "Net change in account balance during the period, converted to a base or reporting currency, if applicable.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "25",
              "175",
              "250",
              "375",
              "and 3190 more..."
            ]
          },
          "period_beginning_converted_balance": {
            "name": "period_beginning_converted_balance",
            "description": "Beginning balance of the period expressed in the base or reporting currency.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1203 more..."
            ]
          },
          "period_ending_converted_balance": {
            "name": "period_ending_converted_balance",
            "description": "Ending balance of the period expressed in the base or reporting currency.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1208 more..."
            ]
          },
          "account_ordinal": {
            "name": "account_ordinal",
            "description": "Integer value indicating the presentation order of the account in financial statements or reports.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3"
            ]
          }
        }
      },
      "quickbooks__cash_flow_statement": {
        "table_name": "quickbooks__cash_flow_statement",
        "fields": {
          "cash_flow_period": {
            "name": "cash_flow_period",
            "description": "The month-end date representing the reporting period for this cash flow statement entry, used to organize cash activity chronologically.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2025-04-01",
              "2025-07-01",
              "2018-07-01",
              "2018-08-01",
              "2018-06-01",
              "and 96 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A broad categorization of the account's fundamental nature within the chart of accounts, such as 'Asset', 'Liability', or 'Equity'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Liability",
              "Equity"
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "An optional internal code for segmenting accounts by organizational or departmental categories, allowing for additional breakdowns in reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "is_sub_account": {
            "name": "is_sub_account",
            "description": "Indicates if the account is a sub-account within a parent-child account hierarchy (True) or a standalone account (False).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "parent_account_number": {
            "name": "parent_account_number",
            "description": "The account number of the parent account, linking sub-accounts to their parent for hierarchical reporting; blank if not a sub-account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1200",
              "1210",
              "1220",
              "1332",
              "and 35 more..."
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The descriptive name of the parent account associated with a sub-account, clarifying account structure and relationships.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Accounts Payable (A/P)",
              "Accounts Receivable (A/R)",
              "Chequing",
              "TD Visa-3534",
              "TD Visa-7463",
              "and 42 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Specifies the account's role in the financial structure, such as 'Bank', 'Fixed Asset', or 'Accounts Payable', to support cash flow and financial reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Credit Card",
              "Other Current Asset",
              "Other Current Liability",
              "Accounts Receivable",
              "Fixed Asset",
              "Bank",
              "Accounts Payable",
              "Equity",
              "Other Asset"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides a more detailed classification within the account type, such as 'Inventory' or 'OtherCurrentAssets', for finer reporting granularity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "CreditCard",
              "AccountsReceivable",
              "OtherCurrentAssets",
              "OtherCurrentLiabilities",
              "AccountsPayable",
              "Checking",
              "OtherFixedAssets",
              "FurnitureAndFixtures",
              "RetainedEarnings",
              "AccumulatedAmortization",
              "OwnersEquity",
              "OrganizationalCosts",
              "UndepositedFunds",
              "OpeningBalanceEquity",
              "Inventory",
              "Savings",
              "Investment_Other",
              "MachineryAndEquipment",
              "GlobalTaxPayable",
              "GlobalTaxSuspense",
              "ShareCapital",
              "CashOnHand"
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "A unique code or identifier assigned to the account for reference in the accounting system, which may be absent for certain accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1060",
              "1200",
              "1210",
              "1220",
              "1332",
              "and 38 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique system-generated identifier for each account, primarily used for linking and referencing accounts across tables.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "55",
              "58",
              "59",
              "60",
              "106",
              "and 45 more..."
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The official name of the account as listed in the chart of accounts, providing a human-readable label for financial entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Accounts Payable (A/P)",
              "Chequing",
              "TD Visa-3534",
              "Due To/From Dan Lambert",
              "TD Visa-7463",
              "and 45 more..."
            ]
          },
          "cash_ending_period": {
            "name": "cash_ending_period",
            "description": "The numerical cash balance in the account at the close of the reporting period, reflecting all inflows and outflows.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1208 more..."
            ]
          },
          "cash_converted_ending_period": {
            "name": "cash_converted_ending_period",
            "description": "The ending period cash balance adjusted for currency conversion or other relevant factors, differing from 'cash_ending_period' only if conversion applies.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1208 more..."
            ]
          },
          "account_unique_id": {
            "name": "account_unique_id",
            "description": "A globally unique identifier for this account-period record, ensuring each table entry is distinct and traceable.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "b3ffc1fa5f3a2d7c435512a352de0e8a",
              "f7eb03b6cccd0efde5853440c7a26cdc",
              "16cf897beeec217d14f0c25bca9a7c3f",
              "f5715cec7c6cd586abc65993690c9f65",
              "99e424307da3463af840136448012895",
              "and 9287 more..."
            ]
          },
          "cash_flow_type": {
            "name": "cash_flow_type",
            "description": "Identifies the nature of cash flow activity (e.g., 'Operating', 'Financing', 'Investing', or 'Cash or Cash Equivalents'), aligning each entry to a cash flow statement section.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Operating",
              "Investing",
              "Cash or Cash Equivalents",
              "Financing"
            ]
          },
          "cash_flow_ordinal": {
            "name": "cash_flow_ordinal",
            "description": "An integer value denoting the sequence or display order of this cash flow entry within its respective section or group.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "cash_beginning_period": {
            "name": "cash_beginning_period",
            "description": "The cash balance in the account at the beginning of the reporting period, serving as the opening amount for cash flow tracking.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1203 more..."
            ]
          },
          "cash_net_period": {
            "name": "cash_net_period",
            "description": "The net change in the account's cash balance during the period, calculated as ending balance minus beginning balance.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "300",
              "20000",
              "-11810",
              "-5254.54",
              "and 1101 more..."
            ]
          },
          "cash_converted_beginning_period": {
            "name": "cash_converted_beginning_period",
            "description": "The opening cash balance for the period, adjusted for currency or conversion factors, differing from 'cash_beginning_period' only if conversion is applicable.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "50",
              "75",
              "100",
              "and 1203 more..."
            ]
          },
          "cash_converted_net_period": {
            "name": "cash_converted_net_period",
            "description": "The net change in cash during the period, adjusted for currency or conversion factors, differing from 'cash_net_period' only if relevant.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "300",
              "20000",
              "-11810",
              "-5254.54",
              "and 1101 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \nTo answer the question, all columns from `quickbooks__general_ledger` are needed, especially those involved in revenue calculation: `adjusted_amount`, `transaction_type`, `account_class`, and others that describe posting and classification. Primary and foreign keys facilitate joins: `unique_id` (PK), `transaction_id`, `account_id`, `customer_id`, and `vendor_id`. For broader context, the column names of all other tables in the schema must be listed. No direct foreign key relationships exist per the ER diagram, but transactional linkage is via common business keys.  \n\n  \n**Table Columns:**  \n- table: quickbooks__general_ledger  \n  columns: unique_id, transaction_id, transaction_index, transaction_date, customer_id, vendor_id, amount, account_id, class_id, account_number, account_name, is_sub_account, parent_account_number, parent_account_name, account_type, account_sub_type, financial_statement_helper, account_current_balance, account_class, transaction_type, transaction_source, account_transaction_type, created_at, updated_at, adjusted_amount, adjusted_converted_amount, running_balance, running_converted_balance  \n  \n- table: quickbooks__balance_sheet  \n  columns: calendar_date, period_first_day, period_last_day, account_class, class_id, is_sub_account, parent_account_number, parent_account_name, account_type, account_sub_type, account_number, account_id, account_name, amount, converted_amount, account_ordinal  \n  \n- table: quickbooks__ap_ar_enhanced  \n  columns: transaction_type, transaction_id, doc_number, transaction_with, customer_vendor_name, customer_vendor_balance, customer_vendor_address_city, customer_vendor_address_line, customer_vendor_website, total_amount, total_converted_amount, current_balance, due_date, is_overdue, days_overdue, initial_payment_date, recent_payment_date, total_current_payment, total_current_converted_payment  \n  \n- table: quickbooks__expenses_sales_enhanced  \n  columns: transaction_source, transaction_id, transaction_line_id, doc_number, transaction_type, transaction_date, item_id, item_quantity, item_unit_price, account_id, account_name, account_sub_type, class_id, customer_id, customer_name, customer_website, vendor_id, vendor_name, billable_status, description, amount, converted_amount, total_amount, total_converted_amount  \n  \n- table: quickbooks__profit_and_loss  \n  columns: calendar_date, period_first_day, period_last_day, account_class, class_id, is_sub_account, parent_account_number, parent_account_name, account_type, account_sub_type, account_number, account_id, account_name, amount, converted_amount, account_ordinal  \n  \n- table: quickbooks__general_ledger_by_period  \n  columns: account_id, account_number, account_name, is_sub_account, parent_account_number, parent_account_name, account_type, account_sub_type, account_class, class_id, financial_statement_helper, date_year, period_first_day, period_last_day, period_net_change, period_beginning_balance, period_ending_balance, period_net_converted_change, period_beginning_converted_balance, period_ending_converted_balance, account_ordinal  \n  \n- table: quickbooks__cash_flow_statement  \n  columns: cash_flow_period, account_class, class_id, is_sub_account, parent_account_number, parent_account_name, account_type, account_sub_type, account_number, account_id, account_name, cash_ending_period, cash_converted_ending_period, account_unique_id, cash_flow_type, cash_flow_ordinal, cash_beginning_period, cash_net_period, cash_converted_beginning_period, cash_converted_net_period  \n  \n  \n**ER Diagram:**  \n- quickbooks__general_ledger (PK: unique_id)\n- quickbooks__ap_ar_enhanced (PK: transaction_id)\n- quickbooks__expenses_sales_enhanced (PK: transaction_id, transaction_line_id)\n- quickbooks__balance_sheet (no explicit PK)\n- quickbooks__profit_and_loss (no explicit PK)\n- quickbooks__general_ledger_by_period (no explicit PK)\n- quickbooks__cash_flow_statement (PK: account_unique_id)\n\nNo explicit foreign key constraints, but business rules describe linkage via transaction_id, customer_id, and vendor_id.\n  \n\n**Relevant Business Rules for Revenue Calculation:**  \n- Use `adjusted_amount` (signed, normalized) for calculations\u2014do not use `amount`.\n- Include only rows where `account_class = 'Revenue'` for revenue reporting.\n- `transaction_type` and `transaction_source` help distinguish revenue postings (e.g., 'invoice', 'deposit').\n- Revenue recognition is based on the posting date in `transaction_date` and the classification in `account_class`.\n  ",
    "other_table_columns": {}
  }
]
````