Metadata of all data sources:

````
[
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "journal_entry_line": {
        "table_name": "journal_entry_line",
        "fields": {
          "journal_entry_id": {
            "name": "journal_entry_id",
            "description": "The unique identifier linking this line item to its parent journal entry, ensuring all related lines are grouped under the same accounting transaction.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "10",
              "16",
              "21",
              "291",
              "1063",
              "and 952 more..."
            ]
          },
          "index": {
            "name": "index",
            "description": "A sequential number indicating the specific position of this line within its parent journal entry, distinguishing each line when multiple entries exist for a single journal entry.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 66 more..."
            ]
          },
          "description": {
            "name": "description",
            "description": "A brief text providing context or details about the nature or purpose of this particular debit or credit line within the journal entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "CPP/QPP Employer",
              "EI Employer",
              "Transfer to Hold Co",
              "To clear AR",
              "Red River Valley Mutual MBNA",
              "and 1041 more..."
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The financial value associated with this line, representing the debit or credit amount recorded for the corresponding account.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "5",
              "10",
              "17",
              "100",
              "and 2526 more..."
            ]
          },
          "posting_type": {
            "name": "posting_type",
            "description": "Specifies whether the line item represents a debit or a credit transaction, fundamental for double-entry accounting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Debit",
              "Credit"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "References the customer related to this line, identifying client-specific transactions such as accounts receivable; null if not customer-related.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "9",
              "11",
              "14",
              "24",
              "29",
              "and 23 more..."
            ]
          },
          "vendor_id": {
            "name": "vendor_id",
            "description": "References the vendor associated with this line, used to identify vendor-specific transactions such as accounts payable; null if not vendor-related.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "35",
              "50",
              "184",
              "225",
              "228",
              "259",
              "293",
              "354",
              "357",
              "415"
            ]
          },
          "tax_applicable_on": {
            "name": "tax_applicable_on",
            "description": "Indicates the transaction context ('Sales' or 'Purchase') in which tax is applied for this line, clarifying whether tax pertains to a sale or purchase.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Sales",
              "Purchase"
            ]
          },
          "tax_amount": {
            "name": "tax_amount",
            "description": "The portion of the line amount attributed to tax, included only when tax is relevant to the transaction.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "225",
              "520",
              "600",
              "675",
              "and 43 more..."
            ]
          },
          "class_id": {
            "name": "class_id",
            "description": "References a classification or category assigned to this line, supporting segmentation and detailed financial reporting; links to the class table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "References the specific ledger account impacted by this line item, identifying whether the entry affects assets, liabilities, income, or expenses.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "19",
              "30",
              "31",
              "33",
              "34",
              "and 107 more..."
            ]
          },
          "tax_code_id": {
            "name": "tax_code_id",
            "description": "References the tax code applied to this line for reporting and compliance purposes, linking to the tax_code table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "The timestamp recording when this record was last updated or synchronized to the database via Fivetran ETL integration.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:00.175000+00:00",
              "2025-08-06 22:10:00.486000+00:00",
              "2025-08-06 22:10:00.484000+00:00",
              "2025-08-06 22:10:00.240000+00:00",
              "2025-08-06 22:10:00.236000+00:00",
              "and 319 more..."
            ]
          }
        }
      },
      "journal_entry": {
        "table_name": "journal_entry",
        "fields": {
          "id": {
            "name": "id",
            "description": "The primary key string uniquely identifying each journal entry header in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5",
              "8",
              "9",
              "13",
              "15",
              "and 952 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "An optional string referencing the external or accounting document number tied to the journal entry, which may be non-unique or missing.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5",
              "7",
              "11",
              "12",
              "13",
              "and 811 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "A string used to manage version control and detect concurrent updates or synchronization changes to the journal entry record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "8",
              "13",
              "14",
              "16",
              "17",
              "64"
            ]
          },
          "private_note": {
            "name": "private_note",
            "description": "A sparsely populated internal string field used for confidential notes, special categorizations, or instructions relevant to the specific journal entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Opening Balance",
              "Reverse of JE ADJ 1",
              "Reverse of JE 496",
              "Reverse of JE 252"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "The precise timestamp indicating when the journal entry record was first created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2019-10-01 08:50:11+00:00",
              "2019-08-01 08:48:46+00:00",
              "2019-12-01 09:51:57+00:00",
              "2018-03-01 09:29:25+00:00",
              "2019-09-01 08:49:03+00:00",
              "and 944 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "The latest timestamp reflecting when any modification was last saved to the journal entry record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-01 09:29:25+00:00",
              "2022-11-20 18:22:49+00:00",
              "2018-02-24 18:31:52+00:00",
              "2018-03-17 17:59:14+00:00",
              "2018-02-24 19:30:20+00:00",
              "and 950 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The date, without time, representing when the journal entry is considered effective for accounting and reporting purposes.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2021-10-31",
              "2020-10-31",
              "2022-10-31",
              "2017-11-01",
              "and 625 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "The timestamp capturing the most recent Fivetran synchronization event for this journal entry record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:00.326000+00:00",
              "2025-08-06 22:10:00.378000+00:00",
              "2025-08-06 22:10:00.351000+00:00",
              "2025-08-06 22:10:00.266000+00:00",
              "2025-08-06 22:10:00.256000+00:00",
              "and 319 more..."
            ]
          }
        }
      },
      "account": {
        "table_name": "account",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated unique identifier for each account record, serving as the primary key within the chart of accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "11",
              "12",
              "13",
              "14",
              "15",
              "and 189 more..."
            ]
          },
          "currency_id": {
            "name": "currency_id",
            "description": "Identifier linking to the 'currency' table, specifying the account's currency; always 'CAD' (Canadian dollars) in this dataset.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "CAD"
            ]
          },
          "description": {
            "name": "description",
            "description": "Optional free-text field used to provide supplementary notes or context for the account, often shared among related accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Any discounts you give your customers on your products or services.",
              "Costs for getting the supplies you use to make your products delivered to you.",
              "Costs for supplies you buy to complete a job or provide a service (not to make products you sell).",
              "Costs for liability insurance for your business.",
              "Money you pay to rent or lease things like office space and storage.",
              "and 32 more..."
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "Globally unique hierarchical path for the account, detailing its position and lineage within nested account structures.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5785",
              "Corporate Tax Receivable",
              "Suspense-Sherry Fontaine (deleted)",
              "Accounts Receivable (A/R):Allowance For Doubtful Accounts",
              "Accounts Receivable (A/R):Accounts Receivable",
              "and 189 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Categorical label denoting the functional group of the account (e.g., 'Accounts Payable'), used for operational grouping and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Income",
              "Other Current Asset",
              "Other Expense",
              "Other Current Liability",
              "Cost of Goods Sold",
              "Accounts Receivable",
              "Fixed Asset",
              "Credit Card",
              "Bank",
              "Accounts Payable",
              "Equity",
              "Other Income",
              "Other Asset"
            ]
          },
          "name": {
            "name": "name",
            "description": "User-facing display name for the account, nearly unique and designed for selection in application interfaces.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "5785",
              "Insurance",
              "Corporate Tax Receivable",
              "Allowance For Doubtful Accounts",
              "Accounts Receivable",
              "and 187 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "Boolean indicator showing if the account is active (true) or inactive/deleted (false) for filtering and soft deletion.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "classification": {
            "name": "classification",
            "description": "High-level financial category (such as 'Asset', 'Liability', 'Expense', etc.) that determines the account's grouping on financial statements.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Expense",
              "Asset",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "sub_account": {
            "name": "sub_account",
            "description": "Boolean flag indicating whether the account is a sub-account (true) nested under a parent, or a top-level account (false).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Fine-grained subtype providing additional specificity within the account_type category, supporting more detailed reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "OfficeGeneralAdministrativeExpenses",
              "OtherMiscellaneousExpense",
              "OtherMiscellaneousServiceCost",
              "OtherCurrentAssets",
              "OtherCurrentLiabilities",
              "and 53 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "Concurrency control token updated with each change to ensure data integrity and manage synchronization across systems.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp recording when the account record was initially created, supporting auditing and lifecycle analysis.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-01-22 13:39:45+00:00",
              "2018-01-22 13:39:44+00:00",
              "2018-02-24 17:32:41+00:00",
              "2018-02-24 17:41:19+00:00",
              "2018-03-30 19:58:23+00:00",
              "and 132 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp of the most recent update to the account record, used for change tracking and audit trails.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 15:57:47+00:00",
              "2021-02-17 23:02:54+00:00",
              "2018-03-30 19:58:23+00:00",
              "2018-03-30 15:48:38+00:00",
              "2018-03-30 19:37:59+00:00",
              "and 179 more..."
            ]
          },
          "balance_with_sub_accounts": {
            "name": "balance_with_sub_accounts",
            "description": "Numeric value representing the account's total balance, inclusive of all direct and indirect sub-accounts for consolidated financial views.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "300",
              "3000",
              "20000",
              "and 32 more..."
            ]
          },
          "balance": {
            "name": "balance",
            "description": "Numeric value of the account's standalone balance, excluding any amounts from sub-accounts.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "300",
              "3000",
              "20000",
              "and 31 more..."
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "Optional internal or external account code, numeric or alphanumeric, used for integrations or as an alternate reference.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "1050",
              "1055",
              "1060",
              "1200",
              "1201",
              "and 123 more..."
            ]
          },
          "parent_account_id": {
            "name": "parent_account_id",
            "description": "Reference to the 'id' of the parent account, establishing hierarchical relationships for sub-accounts; null for top-level accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "58",
              "63",
              "66",
              "73",
              "76",
              "109",
              "112",
              "116",
              "120",
              "152",
              "178"
            ]
          },
          "tax_code_id": {
            "name": "tax_code_id",
            "description": "Optional reference to the default tax code for the account, linking to the 'tax_code' table to support tax calculations and compliance.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "7",
              "8"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "System-managed timestamp marking the last synchronization of this record by Fivetran, used for data pipeline monitoring.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:29.070000+00:00",
              "2025-08-06 22:09:29.047000+00:00",
              "2025-08-06 22:09:29.053000+00:00",
              "2025-08-06 22:09:29.051000+00:00",
              "2025-08-06 22:09:29.059000+00:00",
              "and 39 more..."
            ]
          }
        }
      },
      "class": {
        "table_name": "class",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string identifier that serves as the primary key for each business division (class) record in the table.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1009751",
              "1009752",
              "1009753",
              "1009754",
              "1009755",
              "1025694"
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "A string value used to manage and track changes to the record for synchronization and conflict resolution with external systems.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1"
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "The complete, descriptive name for the business division, providing an unambiguous and unique label for reporting and categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "People Culture",
              "Executive",
              "Accounting Operations",
              "Finance",
              "Global Advisory",
              "Information Technology"
            ]
          },
          "name": {
            "name": "name",
            "description": "A concise, human-readable name for the business division, uniquely identifying the class in user interfaces or selection lists.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "People Culture",
              "Executive",
              "Accounting Operations",
              "Finance",
              "Global Advisory",
              "Information Technology"
            ]
          },
          "active": {
            "name": "active",
            "description": "A boolean flag indicating whether the business division is currently operational (TRUE) or inactive/deprecated (FALSE).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp capturing the exact UTC date and time when the business division record was first created in the system.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-03-23 18:18:52+00:00",
              "2025-03-23 18:19:48+00:00",
              "2025-03-23 18:19:31+00:00",
              "2025-03-23 18:19:13+00:00",
              "2025-04-03 15:53:51+00:00",
              "and 1 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp marking the most recent modification to the business division record, supporting audit and change tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-04-03 15:54:08+00:00",
              "2025-03-23 18:19:48+00:00",
              "2025-03-23 18:19:31+00:00",
              "2025-03-23 18:19:13+00:00",
              "2025-04-03 15:53:51+00:00",
              "and 1 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp automatically maintained by Fivetran, indicating when the record was last synchronized from the source to the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "All distinct values": [
              "2025-08-06 22:09:33.269000+00:00",
              "2025-08-07 02:49:58.198000+00:00",
              "2025-08-07 02:49:58.197000+00:00"
            ]
          }
        }
      },
      "customer": {
        "table_name": "customer",
        "fields": {
          "id": {
            "name": "id",
            "description": "A system-generated unique string serving as the primary key for each customer, used to identify and reference individual customer records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "8",
              "9",
              "10",
              "11",
              "and 380 more..."
            ]
          },
          "family_name": {
            "name": "family_name",
            "description": "The customer's last name or surname, used primarily for individual or contact-based customers and often non-unique.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Penner",
              "Inc",
              "Bell",
              "Deck",
              "Aime",
              "and 103 more..."
            ]
          },
          "given_name": {
            "name": "given_name",
            "description": "The customer's first or given name, typically used for individuals and may appear in multiple records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Dustin",
              "Peter",
              "Craig",
              "Derek",
              "Chris",
              "and 102 more..."
            ]
          },
          "company_name": {
            "name": "company_name",
            "description": "The formal business or organization name linked to the customer; blank for individuals and nearly unique for companies.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "G3 Canada Ltd",
              "OnBusiness Chartered Professional Accountants",
              "Winnipeg School Division",
              "ARTIS Reit",
              "COFCO International",
              "and 336 more..."
            ]
          },
          "display_name": {
            "name": "display_name",
            "description": "The unique, preferred name shown for the customer in user interfaces and reports, which may be a company or person.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "The Boyd Group",
              "OnBusiness Chartered Professional Accountants",
              "Winnipeg School Division",
              "ARTIS Reit",
              "COFCO International",
              "and 380 more..."
            ]
          },
          "balance_with_jobs": {
            "name": "balance_with_jobs",
            "description": "The customer's total outstanding balance including amounts related to associated jobs or projects, reflecting credits and payables.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "5733",
              "7497",
              "8820",
              "10500",
              "and 43 more..."
            ]
          },
          "balance": {
            "name": "balance",
            "description": "The outstanding financial balance for the customer, excluding amounts from jobs, which may be positive or negative.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "5733",
              "7497",
              "8820",
              "10500",
              "and 43 more..."
            ]
          },
          "taxable": {
            "name": "taxable",
            "description": "A boolean flag specifying whether the customer is subject to sales tax (true) or exempt (false).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "created_at": {
            "name": "created_at",
            "description": "The timestamp marking when the customer record was initially created in the system.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2018-03-21 14:17:09+00:00",
              "2018-03-21 14:55:27+00:00",
              "2018-03-21 14:14:16+00:00",
              "2018-03-21 14:49:47+00:00",
              "2018-03-21 16:06:23+00:00",
              "and 380 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "The timestamp showing the most recent update to the customer record, reflecting changes or modifications.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-05-24 14:30:52+00:00",
              "2021-02-23 23:59:47+00:00",
              "2018-11-17 18:28:32+00:00",
              "2018-03-21 18:26:31+00:00",
              "2018-03-21 16:34:40+00:00",
              "and 378 more..."
            ]
          },
          "sales_term_id": {
            "name": "sales_term_id",
            "description": "The reference ID for the sales or payment terms assigned to the customer, linking to the term table for conditions such as payment deadlines.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3",
              "6"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "A system-generated timestamp indicating when the record was last synchronized by the Fivetran ETL pipeline.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:09:36.288000+00:00",
              "2025-08-06 22:09:36.284000+00:00",
              "2025-08-06 22:09:36.286000+00:00",
              "2025-08-06 22:09:36.287000+00:00",
              "2025-08-06 22:09:36.282000+00:00",
              "and 20 more..."
            ]
          }
        }
      },
      "vendor": {
        "table_name": "vendor",
        "fields": {
          "id": {
            "name": "id",
            "description": "System-generated primary key uniquely identifying each vendor in the database.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "3",
              "4",
              "5",
              "6",
              "33",
              "and 257 more..."
            ]
          },
          "family_name": {
            "name": "family_name",
            "description": "Last name of the vendor, populated for individual (non-company) vendors and may repeat across records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Mart",
              "Cafe",
              "Lutz",
              "De Jesus",
              "Chartrand",
              "and 93 more..."
            ]
          },
          "given_name": {
            "name": "given_name",
            "description": "First name of the vendor, used for individual vendors; not guaranteed to be unique or always present.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "The",
              "TD",
              "Manitoba",
              "Adam",
              "Victoria",
              "and 106 more..."
            ]
          },
          "company_name": {
            "name": "company_name",
            "description": "Registered business name for company or organizational vendors; left empty for individuals.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Freshwater Creative",
              "Artis 220 Portage Ltd",
              "Angela Chan",
              "Grand & Toy",
              "Talemetry Inc.",
              "and 173 more..."
            ]
          },
          "active": {
            "name": "active",
            "description": "Indicates if the vendor is currently enabled for transactions (true) or not (false).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "balance": {
            "name": "balance",
            "description": "Current monetary balance with the vendor, showing amounts owed to or by the vendor.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2",
              "-68.53",
              "1184.41",
              "180.25",
              "and 9 more..."
            ]
          },
          "display_name": {
            "name": "display_name",
            "description": "Unique name used in the application UI and reports to identify this vendor record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "Thompson Dorfman Sweatman LLP",
              "Lurd, Kevin",
              "Freshwater Creative",
              "QuickBooks",
              "Artis 220 Portage Ltd",
              "and 257 more..."
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp for when this vendor record was initially created in the system.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-03-21 16:41:12+00:00",
              "2025-07-02 15:42:22+00:00",
              "2018-03-30 14:34:18+00:00",
              "2018-05-19 17:46:29+00:00",
              "2018-03-30 15:39:29+00:00",
              "and 248 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp for the most recent modification made to this vendor record.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-03-21 16:41:12+00:00",
              "2025-07-02 15:42:22+00:00",
              "2018-03-30 14:35:37+00:00",
              "2018-05-19 18:12:05+00:00",
              "2018-03-30 15:40:26+00:00",
              "and 250 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp recording the last successful ETL sync of this record by Fivetran into the data warehouse.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-06 22:10:13.548000+00:00",
              "2025-08-06 22:10:13.550000+00:00",
              "2025-08-06 22:10:13.549000+00:00",
              "2025-08-06 22:10:13.552000+00:00",
              "2025-08-06 22:10:13.551000+00:00",
              "and 5 more..."
            ]
          }
        }
      },
      "tax_code": {
        "table_name": "tax_code",
        "fields": {
          "id": {
            "name": "id",
            "description": "Unique string identifier for each tax code, serving as the table's primary key and reference in related records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11"
            ]
          },
          "active": {
            "name": "active",
            "description": "Boolean flag indicating whether the tax code is currently valid and selectable for transactions.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "description": {
            "name": "description",
            "description": "Detailed textual explanation of the tax code, specifying the type or category of tax it represents (e.g., GST, PST, zero-rated, exempt).",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Tax adjustment",
              "Combined federal and provincial tax (Manitoba)",
              "Provincial sales tax only (Manitoba)",
              "Out of scope",
              "Provincial sales tax (8%) only (Manitoba)",
              "Zero-rated",
              "Federal goods and services tax",
              "Combined federal and provincial tax (8% Manitoba)",
              "Tax-exempt"
            ]
          },
          "name": {
            "name": "name",
            "description": "Short, unique label for the tax code used as a concise reference across the system (e.g., 'GST', 'Exempt', 'GST/PST MB').",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "GST/PST MB",
              "PST MB 2013",
              "PST MB Adjustment",
              "PST MB",
              "GST",
              "Zero-rated",
              "Out of Scope",
              "GST/PST MB 2013",
              "GST/HST Adjustment",
              "Exempt"
            ]
          },
          "taxable": {
            "name": "taxable",
            "description": "Boolean value specifying if the tax code results in a taxable transaction (TRUE) or is non-taxable (FALSE).",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "tax_group": {
            "name": "tax_group",
            "description": "Boolean flag indicating whether this tax code is part of a combined group of taxes, allowing aggregation with other tax codes.",
            "dataType": "BOOL",
            "is_unstructured": false
          },
          "created_at": {
            "name": "created_at",
            "description": "Timestamp recording when the tax code entry was initially created in the database.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "All distinct values": [
              "2018-02-24 17:32:41+00:00"
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "Timestamp marking the most recent update to the tax code record, reflecting any changes since creation.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "All distinct values": [
              "2018-02-24 17:32:41+00:00"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "Timestamp denoting the last time this record was synchronized by the Fivetran ETL integration, used for data pipeline auditing.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-08-07 02:50:08.349000+00:00",
              "2025-08-07 02:50:08.344000+00:00",
              "2025-08-07 02:50:08.343000+00:00",
              "2025-08-07 02:50:08.341000+00:00",
              "2025-08-07 02:50:08.345000+00:00",
              "and 2 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n\n**Reasoning:**  \n\n\nI identified tables and columns related to general ledger transactions by looking for those capturing journal entries and their lines, with fields such as transaction_date, amount (as adjusted_amount analog), account_id, class, customer/vendor, and account information. I excluded the invalid table and focused only on native tables from the QuickBooks schema with appropriate metadata. I include primary/foreign keys needed for joins.  \n\n\n**Table Columns:**  \n\n\n- table: journal_entry_line  \n  columns: journal_entry_id, index, description, amount, posting_type, customer_id, vendor_id, tax_applicable_on, tax_amount, class_id, account_id, tax_code_id, _fivetran_synced  \n\n- table: journal_entry  \n  columns: id, doc_number, sync_token, private_note, created_at, updated_at, transaction_date, _fivetran_synced  \n\n- table: account  \n  columns: id, currency_id, description, fully_qualified_name, account_type, name, active, classification, sub_account, account_sub_type, sync_token, created_at, updated_at, balance_with_sub_accounts, balance, account_number, parent_account_id, tax_code_id, _fivetran_synced  \n\n- table: class  \n  columns: id, sync_token, fully_qualified_name, name, active, created_at, updated_at, _fivetran_synced  \n\n- table: customer  \n  columns: id, display_name, company_name, family_name, given_name, balance, balance_with_jobs, taxable, sales_term_id, created_at, updated_at, _fivetran_synced  \n\n- table: vendor  \n  columns: id, display_name, company_name, family_name, given_name, balance, active, created_at, updated_at, _fivetran_synced  \n\n- table: tax_code  \n  columns: id, name, description, taxable, tax_group, active, created_at, updated_at, _fivetran_synced  ",
    "other_table_columns": {
      "item": [
        "id",
        "fully_qualified_name",
        "name",
        "sync_token",
        "type",
        "sales_tax_included",
        "created_at",
        "updated_at",
        "income_account_id",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [
        "invoice_id",
        "index",
        "line_num",
        "description",
        "amount",
        "unit_price",
        "item_id",
        "class_id",
        "quantity",
        "account_id",
        "tax_code_id",
        "_fivetran_synced"
      ],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [
        "id",
        "sync_token",
        "balance",
        "doc_number",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "due_date",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "estimate_line": [],
      "journal_entry_tax_line": [
        "journal_entry_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "term": [
        "id",
        "name",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [
        "purchase_id",
        "index",
        "amount",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "credit_card_payment_txn": [
        "id",
        "amount",
        "currency_id",
        "created_at",
        "updated_at",
        "transaction_date",
        "bank_account_id",
        "credit_card_account_id",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_type_applicable",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [
        "bill_id",
        "index",
        "bill_payment_id",
        "_fivetran_synced"
      ],
      "transfer": [
        "id",
        "amount",
        "private_note",
        "created_at",
        "updated_at",
        "currency_id",
        "transaction_date",
        "to_account_id",
        "from_account_id",
        "_fivetran_synced"
      ],
      "vendor": [
        "print_on_check_name",
        "middle_name",
        "sync_token",
        "billing_address_id"
      ],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [
        "invoice_id",
        "index",
        "payment_id",
        "_fivetran_synced"
      ],
      "deposit_line": [
        "deposit_id",
        "index",
        "description",
        "detail_type",
        "amount",
        "deposit_tax_applicable_on",
        "deposit_tax_code_id",
        "deposit_class_id",
        "deposit_account_id",
        "deposit_customer_id",
        "_fivetran_synced"
      ],
      "tax_agency": [
        "id",
        "display_name",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [
        "id",
        "pay_type",
        "doc_number",
        "sync_token",
        "currency_id",
        "check_print_status",
        "check_bank_account_id",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "vendor_id",
        "_fivetran_synced"
      ],
      "address": [
        "id",
        "line_1",
        "line_2",
        "line_3",
        "line_4",
        "city",
        "postal_code",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [
        "id",
        "email_status",
        "doc_number",
        "sync_token",
        "global_tax_calculation",
        "shipping_address_id",
        "billing_address_id",
        "bill_email",
        "customer_memo",
        "total_tax",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "bill_payment_line": [
        "bill_payment_id",
        "index",
        "amount",
        "bill_id",
        "journal_entry_id",
        "_fivetran_synced"
      ],
      "payment_line": [
        "payment_id",
        "index",
        "amount",
        "journal_entry_id",
        "credit_memo_id",
        "invoice_id",
        "_fivetran_synced"
      ],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "bill_line": [
        "bill_id",
        "index",
        "description",
        "amount",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "_fivetran_synced"
      ],
      "payment": [
        "id",
        "sync_token",
        "created_at",
        "updated_at",
        "reference_number",
        "unapplied_amount",
        "total_amount",
        "transaction_date",
        "payment_method_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "id",
        "email_status",
        "doc_number",
        "print_status",
        "balance",
        "sync_token",
        "global_tax_calculation",
        "delivery_time",
        "created_at",
        "updated_at",
        "billing_email",
        "customer_memo",
        "total_tax",
        "shipping_address_id",
        "billing_address_id",
        "due_date",
        "total_amount",
        "transaction_date",
        "sales_term_id",
        "customer_id",
        "_fivetran_synced"
      ],
      "purchase": [
        "id",
        "sync_token",
        "credit",
        "doc_number",
        "payment_type",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "customer_id",
        "vendor_id",
        "remit_to_address_id",
        "total_tax",
        "total_amount",
        "transaction_date",
        "account_id",
        "payment_method_id",
        "_fivetran_synced"
      ],
      "customer": [
        "fully_qualified_name",
        "print_on_check_name",
        "title",
        "middle_name",
        "active",
        "sync_token",
        "email",
        "website",
        "phone_number",
        "mobile_number",
        "shipping_address_id",
        "bill_address_id",
        "default_tax_code_id"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "invoice_id",
        "index",
        "id",
        "line_num",
        "description",
        "amount",
        "detail_type",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_class_id",
        "sales_item_quantity",
        "sales_item_account_id",
        "sales_item_tax_code_id",
        "bundle_quantity",
        "_fivetran_synced"
      ],
      "credit_memo_line": [
        "credit_memo_id",
        "index",
        "description",
        "amount",
        "sales_item_unit_price",
        "sales_item_item_id",
        "sales_item_account_id",
        "_fivetran_synced"
      ],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "payment_method": [
        "id",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [
        "purchase_id",
        "index",
        "amount",
        "description",
        "account_expense_class_id",
        "account_expense_tax_code_id",
        "account_expense_account_id",
        "account_expense_customer_id",
        "_fivetran_synced"
      ],
      "deposit": [
        "id",
        "sync_token",
        "private_note",
        "global_tax_calculation",
        "created_at",
        "updated_at",
        "total_amount",
        "transaction_date",
        "account_id",
        "_fivetran_synced"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Reports",
    "selected_table_column_metadata": {
      "quickbooks__general_ledger": {
        "table_name": "quickbooks__general_ledger",
        "fields": {
          "transaction_date": {
            "name": "transaction_date",
            "description": "The effective date on which the accounting transaction was recognized in the ledger.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2018-10-31",
              "2017-11-01",
              "2017-10-31",
              "2020-10-31",
              "2017-11-30",
              "and 1932 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A high-level grouping of the account for financial reporting, such as 'Revenue', 'Asset', 'Expense', 'Liability', or 'Equity'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Expense",
              "Liability",
              "Revenue",
              "Equity"
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Specifies whether this ledger line is a debit or a credit, defining its effect on the account balance.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "adjusted_amount": {
            "name": "adjusted_amount",
            "description": "The monetary value for this entry after any corrections or adjustments have been applied, which may differ from the original amount.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "2500",
              "3123",
              "8000",
              "8400",
              "and 7488 more..."
            ]
          }
        }
      }
    },
    "reasoning": "**Reasoning:**  \n\n\nThe user question centers on calculating revenue using general ledger transaction data, specifically requesting columns like transaction_date, adjusted_amount, transaction_type, and account_class. Only tables from the provided metadata can be used. The core table is `quickbooks__general_ledger`, as it contains all requested fields. Other tables (like those for account/customer/vendor) are not present in the provided list, so only related tables from the given set can be included for context if needed.  \n\n\n**Table Columns:**  \n\n\n- table: quickbooks__general_ledger  \n  columns: unique_id, transaction_id, transaction_date, adjusted_amount, transaction_type, account_class, account_id, account_number, account_name, account_type, account_sub_type, class_id, customer_id, vendor_id, financial_statement_helper, transaction_source, amount, adjusted_converted_amount, running_balance, running_converted_balance, created_at, updated_at, account_current_balance, parent_account_number, parent_account_name, is_sub_account, account_transaction_type  \n\n\n- table: quickbooks__profit_and_loss  \n  columns: account_class, account_type, account_sub_type, account_id, account_name, account_number, amount, converted_amount, calendar_date, period_first_day, period_last_day, class_id  \n\n\n- table: quickbooks__general_ledger_by_period  \n  columns: account_id, account_number, account_name, account_type, account_sub_type, account_class, class_id, financial_statement_helper, date_year, period_first_day, period_last_day, period_net_change, period_beginning_balance, period_ending_balance, period_net_converted_change, period_beginning_converted_balance, period_ending_converted_balance, account_ordinal, is_sub_account, parent_account_number, parent_account_name  \n\n\n---\n\n**ER Diagram (Key Relationships, First Cycle):**  \n\n```json\n{\n  \"entities\": [\n    {\n      \"name\": \"quickbooks__general_ledger\",\n      \"primaryKeys\": [[\"unique_id\"]]\n    },\n    {\n      \"name\": \"quickbooks__profit_and_loss\",\n      \"primaryKeys\": []\n    },\n    {\n      \"name\": \"quickbooks__general_ledger_by_period\",\n      \"primaryKeys\": []\n    }\n  ],\n  \"relationships\": []\n}\n```\n\n---\n\n**Other Tables/Columns for Context:**  \n\n- quickbooks__expenses_sales_enhanced: transaction_id, transaction_type, account_class, account_id, transaction_date, amount, converted_amount, class_id, customer_id, vendor_id  \n- quickbooks__ap_ar_enhanced: transaction_id, transaction_type, total_amount, total_converted_amount, customer_vendor_name, customer_vendor_balance, due_date, customer_vendor_address_city, recent_payment_date, is_overdue  \n- quickbooks__balance_sheet: account_id, account_class, account_type, account_sub_type, account_name, account_number, amount, converted_amount, calendar_date, period_first_day, period_last_day, class_id  \n- quickbooks__cash_flow_statement: account_id, account_class, account_type, account_sub_type, account_name, account_number, cash_flow_period, cash_ending_period, cash_flow_type, class_id\n\n---",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "transaction_source",
        "transaction_id",
        "transaction_line_id",
        "doc_number",
        "transaction_type",
        "transaction_date",
        "item_id",
        "item_quantity",
        "item_unit_price",
        "account_id",
        "account_name",
        "account_sub_type",
        "class_id",
        "customer_id",
        "customer_name",
        "customer_website",
        "vendor_id",
        "vendor_name",
        "billable_status",
        "description",
        "amount",
        "converted_amount",
        "total_amount",
        "total_converted_amount"
      ],
      "quickbooks__cash_flow_statement": [
        "cash_flow_period",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "cash_ending_period",
        "cash_converted_ending_period",
        "account_unique_id",
        "cash_flow_type",
        "cash_flow_ordinal",
        "cash_beginning_period",
        "cash_net_period",
        "cash_converted_beginning_period",
        "cash_converted_net_period"
      ],
      "quickbooks__profit_and_loss": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__balance_sheet": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "class_id",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__ap_ar_enhanced": [
        "transaction_type",
        "transaction_id",
        "doc_number",
        "transaction_with",
        "customer_vendor_name",
        "customer_vendor_balance",
        "customer_vendor_address_city",
        "customer_vendor_address_line",
        "customer_vendor_website",
        "total_amount",
        "total_converted_amount",
        "current_balance",
        "due_date",
        "is_overdue",
        "days_overdue",
        "initial_payment_date",
        "recent_payment_date",
        "total_current_payment",
        "total_current_converted_payment"
      ],
      "quickbooks__general_ledger_by_period": [
        "account_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_class",
        "class_id",
        "financial_statement_helper",
        "date_year",
        "period_first_day",
        "period_last_day",
        "period_net_change",
        "period_beginning_balance",
        "period_ending_balance",
        "period_net_converted_change",
        "period_beginning_converted_balance",
        "period_ending_converted_balance",
        "account_ordinal"
      ],
      "quickbooks__general_ledger": [
        "unique_id",
        "transaction_id",
        "transaction_index",
        "customer_id",
        "vendor_id",
        "amount",
        "account_id",
        "class_id",
        "account_number",
        "account_name",
        "is_sub_account",
        "parent_account_number",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "financial_statement_helper",
        "account_current_balance",
        "transaction_source",
        "account_transaction_type",
        "created_at",
        "updated_at",
        "adjusted_converted_amount",
        "running_balance",
        "running_converted_balance"
      ]
    }
  }
]
````