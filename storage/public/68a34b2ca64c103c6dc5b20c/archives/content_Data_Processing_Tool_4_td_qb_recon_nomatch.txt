Python code:

```python
import pandas as pd
import numpy as np
from datetime import datetime
from metalake import load_data
import os
import difflib

chat_id = "68a34deda64c103c6dc5b3a0"

# --- Load Data ---
bank_file = 'TD_bank_payments_all_data.dat'
qb_file = 'bill_payment_lines_recon.dat'

bank_df = load_data(chat_id, bank_file)
qb_df = load_data(chat_id, qb_file)

if bank_df.empty or qb_df.empty:
    print("One or both input files are empty. Please check the data sources.")
else:
    # --- 1. BANK DATA ---
    # Keep columns: Date -> bank_tx_date, Payee, DESCRIPTION, Memo/Description, Total -> bank_tx_amount
    col_map = {
        'Date': 'bank_tx_date',
        'Payee': 'Payee',
        'DESCRIPTION': 'DESCRIPTION',
        'Memo/Description': 'Memo/Description',
        'Total': 'bank_tx_amount'  # Some files use 'Total', some 'AMOUNT', etc.
    }
    # Find the actual column names present
    cols_to_use = {}
    for k, v in col_map.items():
        if k in bank_df.columns:
            cols_to_use[k] = v
    # Try to get 'Total' or 'AMOUNT' for the amount field
    if 'Total' not in bank_df.columns and 'AMOUNT' in bank_df.columns:
        cols_to_use['AMOUNT'] = 'bank_tx_amount'
    bank_working = bank_df[list(cols_to_use.keys())].copy()
    bank_working.rename(columns=cols_to_use, inplace=True)
    # Ensure all needed columns are present
    for col in ['Payee', 'DESCRIPTION', 'Memo/Description']:
        if col not in bank_working.columns:
            bank_working[col] = None
    # Compose bank_tx_description
    def first_nonnull(*args):
        for a in args:
            if pd.notnull(a) and str(a).strip():
                return str(a).strip()
        return ''
    bank_working['bank_tx_description'] = bank_working.apply(
        lambda row: first_nonnull(row.get('Payee'), row.get('DESCRIPTION'), row.get('Memo/Description')),
        axis=1
    )
    # Parse bank_tx_date (MM/DD/YYYY)
    def parse_bank_date(s):
        if pd.isnull(s):
            return pd.NaT
        try:
            return pd.to_datetime(s, format="%m/%d/%Y", errors='coerce')
        except Exception:
            return pd.to_datetime(s, errors='coerce')
    bank_working['bank_tx_date'] = bank_working['bank_tx_date'].apply(parse_bank_date)
    # Cast bank_tx_amount to absolute float
    def parse_amount(val):
        if pd.isnull(val):
            return np.nan
        try:
            return abs(float(val))
        except Exception:
            try:
                # Remove commas/dollar sign if present
                return abs(float(str(val).replace(',', '').replace('$', '')))
            except:
                return np.nan
    bank_working['bank_tx_amount'] = bank_working['bank_tx_amount'].apply(parse_amount)

    # --- 2. QUICKBOOKS DATA ---
    # payment_date: {'value': 'YYYY-MM-DD'}
    def strip_json_date(val):
        if pd.isnull(val):
            return pd.NaT
        if isinstance(val, dict):
            v = val.get('value', None)
        else:
            v = str(val)
            if v.startswith("{'value': "):
                v = v.replace("{'value': '", '').replace("'}", '')
        try:
            return pd.to_datetime(v, format="%Y-%m-%d", errors='coerce')
        except Exception:
            return pd.to_datetime(v, errors='coerce')
    qb_df['payment_date'] = qb_df['payment_date'].apply(strip_json_date)
    # Aggregate to one row per bill_payment_id
    group_cols = ['bill_payment_id', 'payment_date', 'payment_total_amount', 'vendor_display_name',
                  'print_on_check_name', 'doc_number']
    qb_agg = qb_df.groupby('bill_payment_id', as_index=False).agg({
        'payment_date': 'first',
        'payment_total_amount': 'first',
        'vendor_display_name': 'first',
        'print_on_check_name': 'first',
        'doc_number': 'first'
    })
    qb_agg['payment_total_amount'] = qb_agg['payment_total_amount'].apply(parse_amount)
    # vendor_name = first non-null of display_name, print_on_check_name
    qb_agg['vendor_name'] = qb_agg.apply(
        lambda row: first_nonnull(row.get('vendor_display_name'), row.get('print_on_check_name')),
        axis=1
    )

    # --- 3. MATCHING LOGIC ---
    # Fuzzy similarity: use difflib.SequenceMatcher (ratio)
    def fuzzy_similarity(a, b):
        a = str(a or '').lower().strip()
        b = str(b or '').lower().strip()
        if not a or not b:
            return 0.0
        return difflib.SequenceMatcher(None, a, b).ratio()

    results = []
    for idx, bank_row in bank_working.iterrows():
        bdate = bank_row['bank_tx_date']
        bdesc = bank_row['bank_tx_description']
        bamt = bank_row['bank_tx_amount']
        # Pre-filter QB by non-null payment_total_amount
        candidates = qb_agg[qb_agg['payment_total_amount'].notnull()].copy()
        # Compute abs(amount diff) and similarity
        candidates['amount_diff'] = (candidates['payment_total_amount'] - bamt).abs()
        candidates['name_similarity'] = candidates['vendor_name'].apply(lambda x: fuzzy_similarity(bdesc, x))
        # --- Primary rule ---
        rule1 = (candidates['amount_diff'] <= 1.00) & (candidates['name_similarity'] >= 0.80)
        matched = candidates[rule1].copy()
        # --- Secondary rule if no match ---
        if matched.empty:
            amt_2pct = candidates['payment_total_amount'] * 0.02
            rule2 = (candidates['amount_diff'] <= amt_2pct) & (candidates['name_similarity'] >= 0.85)
            matched = candidates[rule2].copy()
        # If multiple, pick by date diff (prefer <=7 days)
        match_idx = None
        if not matched.empty:
            matched['date_diff'] = matched['payment_date'].apply(
                lambda x: abs((bdate - x).days) if pd.notnull(bdate) and pd.notnull(x) else np.inf)
            # Prefer matches with date_diff <=7 days
            prefer = matched[matched['date_diff'] <= 7]
            if not prefer.empty:
                min_date_diff = prefer['date_diff'].min()
                top = prefer[prefer['date_diff'] == min_date_diff]
            else:
                min_date_diff = matched['date_diff'].min()
                top = matched[matched['date_diff'] == min_date_diff]
            # If still tied, choose earliest payment_date
            if len(top) > 1:
                top = top.sort_values('payment_date')
            match_row = top.iloc[0]
            # Compose result row
            results.append({
                'bank_tx_date': bdate,
                'bank_tx_description': bdesc,
                'bank_tx_amount': bamt,
                'bill_payment_id': match_row['bill_payment_id'],
                'vendor_name': match_row['vendor_name'],
                'payment_date': match_row['payment_date'],
                'bill_description': match_row['doc_number'],
                'additional_info': f"sim={match_row['name_similarity']:.2f}, amt_diff={match_row['amount_diff']:.2f}, date_diff={match_row['date_diff']}"
            })
        else:
            # No match found
            results.append({
                'bank_tx_date': bdate,
                'bank_tx_description': bdesc,
                'bank_tx_amount': bamt,
                'bill_payment_id': '',
                'vendor_name': '',
                'payment_date': '',
                'bill_description': '',
                'additional_info': 'No match'
            })

    # --- 4. OUTPUT ---
    result_df = pd.DataFrame(results, columns=[
        'bank_tx_date', 'bank_tx_description', 'bank_tx_amount',
        'bill_payment_id', 'vendor_name', 'payment_date', 'bill_description', 'additional_info'
    ])
    os.makedirs('files', exist_ok=True)
    out_csv = 'files/matched_results.csv'
    out_dat = 'files/matched_results.dat'
    result_df.to_csv(out_csv, index=False)
    result_df.to_csv(out_dat, index=False)
    # Display markdown preview (first 15 rows)
    preview = result_df.head(15)
    print(preview.to_markdown(index=False))
    if len(result_df) > 15:
        print(f"\n(Only first 15 of {len(result_df)} rows shown above.)")
````

Output:

| bank_tx_date        | bank_tx_description           |   bank_tx_amount | bill_payment_id   | vendor_name   | payment_date   | bill_description   | additional_info   |
|:--------------------|:------------------------------|-----------------:|:------------------|:--------------|:---------------|:-------------------|:------------------|
| 2025-06-30 00:00:00 | Tuxedo Golf Course            |            28.8  |                   |               |                |                    | No match          |
| 2025-06-30 00:00:00 | Tuxedo Golf Course            |            27    |                   |               |                |                    | No match          |
| 2025-06-30 00:00:00 | Uber                          |             1    |                   |               |                |                    | No match          |
| 2025-06-30 00:00:00 | Faber Creative                |          1048.95 |                   |               |                |                    | No match          |
| 2025-06-30 00:00:00 | TD Bank                       |           125    |                   |               |                |                    | No match          |
| 2025-06-30 00:00:00 | TD Bank                       |            15    |                   |               |                |                    | No match          |
| 2025-06-29 00:00:00 | Joey Restaurant               |           165.32 |                   |               |                |                    | No match          |
| 2025-06-27 00:00:00 | 4 Seasons Heating & Cooling   |           168    |                   |               |                |                    | No match          |
| 2025-06-27 00:00:00 | Better Proposals              |           137.98 |                   |               |                |                    | No match          |
| 2025-06-27 00:00:00 | Lourdes Azpillaga             |          3503    |                   |               |                |                    | No match          |
| 2025-06-27 00:00:00 | Joanie Cassidy                |          2098.23 |                   |               |                |                    | No match          |
| 2025-06-27 00:00:00 | Mario Ricardo Velasquez Ramos |          2035.54 |                   |               |                |                    | No match          |
| 2025-06-27 00:00:00 | Yarenis Fernandez             |          3782.54 |                   |               |                |                    | No match          |
| 2025-06-26 00:00:00 | The Keg                       |           149.43 |                   |               |                |                    | No match          |
| 2025-06-26 00:00:00 | Bistro Inferno                |           120.96 |                   |               |                |                    | No match          |

(Only first 15 of 714 rows shown above.)

