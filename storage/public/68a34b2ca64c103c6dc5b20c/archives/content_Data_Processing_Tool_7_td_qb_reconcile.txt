Python code:

```python
import pandas as pd
import numpy as np
from datetime import datetime
from metalake import load_data
import os
import difflib

def first_nonnull(*args):
    for a in args:
        if pd.notnull(a) and str(a).strip():
            return str(a).strip()
    return ''

def parse_amount(val):
    if pd.isnull(val):
        return np.nan
    try:
        return abs(float(val))
    except Exception:
        try:
            return abs(float(str(val).replace(',', '').replace('$', '')))
        except:
            return np.nan

def fuzzy_similarity(a, b):
    a = str(a or '').lower().strip()
    b = str(b or '').lower().strip()
    if not a or not b:
        return 0.0
    return difflib.SequenceMatcher(None, a, b).ratio()

def parse_bank_date(s):
    if pd.isnull(s):
        return pd.NaT
    try:
        return pd.to_datetime(s, format="%m/%d/%Y", errors='coerce')
    except Exception:
        return pd.to_datetime(s, errors='coerce')

def strip_json_date(val):
    if pd.isnull(val):
        return pd.NaT
    if isinstance(val, dict):
        v = val.get('value', None)
    else:
        v = str(val)
        if v.startswith("{'value': "):
            v = v.replace("{'value': '", '').replace("'}", '')
    try:
        return pd.to_datetime(v, format="%Y-%m-%d", errors='coerce')
    except Exception:
        return pd.to_datetime(v, errors='coerce')

# --- Load Data ---
chat_id = "68a34eeaa64c103c6dc5b431"
bank_file = 'TD_bank_payments_all_data.dat'
qb_file = 'bill_payment_lines_recon.dat'
purch_file = 'purchase_cash_creditcard_expenses.dat'

bank_df = load_data(chat_id, bank_file)
qb_df = load_data(chat_id, qb_file)
purch_df = load_data(chat_id, purch_file)

if bank_df.empty or qb_df.empty or purch_df.empty:
    print("One or more input files are empty. Please check the data sources.")
else:
    # --- 1. BANK DATA ---
    col_map = {
        'Date': 'bank_tx_date',
        'Payee': 'Payee',
        'DESCRIPTION': 'DESCRIPTION',
        'Memo/Description': 'Memo/Description',
        'Total': 'bank_tx_amount'
    }
    cols_to_use = {}
    for k, v in col_map.items():
        if k in bank_df.columns:
            cols_to_use[k] = v
    if 'Total' not in bank_df.columns and 'AMOUNT' in bank_df.columns:
        cols_to_use['AMOUNT'] = 'bank_tx_amount'
    bank_working = bank_df[list(cols_to_use.keys())].copy()
    bank_working.rename(columns=cols_to_use, inplace=True)
    for col in ['Payee', 'DESCRIPTION', 'Memo/Description']:
        if col not in bank_working.columns:
            bank_working[col] = None
    bank_working['bank_tx_description'] = bank_working.apply(
        lambda row: first_nonnull(row.get('Payee'), row.get('DESCRIPTION'), row.get('Memo/Description')),
        axis=1
    )
    bank_working['bank_tx_date'] = bank_working['bank_tx_date'].apply(parse_bank_date)
    bank_working['bank_tx_amount'] = bank_working['bank_tx_amount'].apply(parse_amount)
    bank_working = bank_working[['bank_tx_date', 'bank_tx_description', 'bank_tx_amount']]

    # --- 2. QUICKBOOKS DATA ---
    # Bill payments
    qb_df['payment_date'] = qb_df['payment_date'].apply(strip_json_date)
    qb_bill = qb_df.groupby('bill_payment_id', as_index=False).agg({
        'payment_date': 'first',
        'payment_total_amount': 'first',
        'vendor_display_name': 'first',
        'print_on_check_name': 'first',
        'doc_number': 'first'
    })
    qb_bill['payment_amount'] = qb_bill['payment_total_amount'].apply(parse_amount)
    qb_bill['vendor_name'] = qb_bill.apply(lambda row: first_nonnull(row.get('vendor_display_name'), row.get('print_on_check_name')), axis=1)
    qb_bill['txn_id'] = qb_bill['bill_payment_id']
    qb_bill['txn_type'] = 'BillPayment'
    qb_bill['doc_number'] = qb_bill['doc_number']
    qb_bill = qb_bill[['txn_id', 'txn_type', 'vendor_name', 'payment_date', 'payment_amount', 'doc_number']]

    # Purchases
    purch_df['payment_date'] = purch_df['purchase_date'].apply(strip_json_date)
    purch_df['payment_amount'] = purch_df['purchase_total_amount'].apply(parse_amount)
    purch_df['vendor_name'] = purch_df.apply(lambda row: first_nonnull(row.get('vendor_display_name'), row.get('print_on_check_name')), axis=1)
    purch_df['txn_id'] = purch_df['purchase_id']
    purch_df['txn_type'] = 'Purchase'
    purch_df['doc_number'] = purch_df['doc_number']
    purch_working = purch_df[['txn_id', 'txn_type', 'vendor_name', 'payment_date', 'payment_amount', 'doc_number']]

    # Combine candidates
    candidates = pd.concat([qb_bill, purch_working], axis=0, ignore_index=True)
    candidates = candidates[candidates['payment_amount'].notnull()].copy()

    # --- 3. MATCHING CASCADE ---
    results = []
    for idx, bank_row in bank_working.iterrows():
        bdate = bank_row['bank_tx_date']
        bdesc = bank_row['bank_tx_description']
        bamt = bank_row['bank_tx_amount']
        cand = candidates.copy()
        cand['amount_diff'] = (cand['payment_amount'] - bamt).abs()
        cand['date_diff'] = cand['payment_date'].apply(lambda x: abs((bdate - x).days) if pd.notnull(bdate) and pd.notnull(x) else np.inf)
        cand['fuzzy_score'] = cand['vendor_name'].apply(lambda x: fuzzy_similarity(bdesc, x))
        # Level 1
        level1 = cand[(cand['amount_diff'] <= 1.00) & (cand['fuzzy_score'] >= 0.80)]
        # Level 2
        level2 = cand[(cand['amount_diff'] <= 1.00) & (cand['date_diff'] <= 3)]
        # Level 3
        level3 = cand[(cand['amount_diff'] <= (0.005 * cand['payment_amount'])) & (cand['date_diff'] <= 3)]
        # Level 4
        level4 = cand[(cand['amount_diff'] <= 2.00) & (cand['date_diff'] <= 7)]
        match = None
        match_level = 0
        if len(level1) == 1:
            match = level1.iloc[0]
            match_level = 1
        elif len(level1) > 1:
            top = level1[level1['date_diff'] == level1['date_diff'].min()]
            match = top.sort_values('payment_date').iloc[0]
            match_level = 1
        elif len(level2) == 1:
            match = level2.iloc[0]
            match_level = 2
        elif len(level2) > 1:
            top = level2[level2['date_diff'] == level2['date_diff'].min()]
            match = top.sort_values('payment_date').iloc[0]
            match_level = 2
        elif len(level3) == 1:
            match = level3.iloc[0]
            match_level = 3
        elif len(level3) > 1:
            top = level3[level3['date_diff'] == level3['date_diff'].min()]
            match = top.sort_values('payment_date').iloc[0]
            match_level = 3
        elif len(level4) == 1:
            match = level4.iloc[0]
            match_level = 4
        elif len(level4) > 1:
            top = level4[level4['date_diff'] == level4['date_diff'].min()]
            match = top.sort_values('payment_date').iloc[0]
            match_level = 4
        if match is not None:
            add_info = f"amount_diff={match['amount_diff']:.2f}, date_diff={int(match['date_diff'])}"
            if match_level == 1:
                add_info += f", fuzzy_score={match['fuzzy_score']:.2f}"
            results.append({
                'bank_tx_date': bdate,
                'bank_tx_description': bdesc,
                'bank_tx_amount': bamt,
                'txn_id': match['txn_id'],
                'txn_type': match['txn_type'],
                'vendor_name': match['vendor_name'],
                'payment_date': match['payment_date'],
                'bill_description': match['doc_number'],
                'match_level': match_level,
                'additional_info': add_info
            })
        else:
            results.append({
                'bank_tx_date': bdate,
                'bank_tx_description': bdesc,
                'bank_tx_amount': bamt,
                'txn_id': '',
                'txn_type': '',
                'vendor_name': '',
                'payment_date': '',
                'bill_description': '',
                'match_level': 0,
                'additional_info': 'No match'
            })
    # --- 4. OUTPUT ---
    result_df = pd.DataFrame(results, columns=[
        'bank_tx_date', 'bank_tx_description', 'bank_tx_amount',
        'txn_id', 'txn_type', 'vendor_name', 'payment_date', 'bill_description',
        'match_level', 'additional_info']
    )
    os.makedirs('files', exist_ok=True)
    out_csv = 'files/matched_results_v3.csv'
    out_dat = 'files/matched_results_v3.dat'
    result_df.to_csv(out_csv, index=False)
    result_df.to_csv(out_dat, index=False)
    # Markdown preview (first 15 rows)
    preview = result_df.head(15)
    print(preview.to_markdown(index=False))
    if len(result_df) > 15:
        print(f"\n(Only first 15 of {len(result_df)} rows shown above.)")

````

Output:

| bank_tx_date        | bank_tx_description           |   bank_tx_amount |   txn_id | txn_type   | vendor_name                   | payment_date        | bill_description   |   match_level | additional_info                                 |
|:--------------------|:------------------------------|-----------------:|---------:|:-----------|:------------------------------|:--------------------|:-------------------|--------------:|:------------------------------------------------|
| 2025-06-30 00:00:00 | Tuxedo Golf Course            |            28.8  |     6935 | Purchase   | Tuxedo Golf Course            | 2025-06-30 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-30 00:00:00 | Tuxedo Golf Course            |            27    |     6934 | Purchase   | Tuxedo Golf Course            | 2025-06-30 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-30 00:00:00 | Uber                          |             1    |     6933 | Purchase   | Uber                          | 2025-06-30 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-30 00:00:00 | Faber Creative                |          1048.95 |     6932 | Purchase   | Faber Creative                | 2025-06-30 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-30 00:00:00 | TD Bank                       |           125    |     6890 | Purchase   | TD Bank                       | 2025-06-30 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-30 00:00:00 | TD Bank                       |            15    |     6889 | Purchase   | TD Bank                       | 2025-06-30 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-29 00:00:00 | Joey Restaurant               |           165.32 |     6954 | Purchase   | Joey Restaurant               | 2025-06-29 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-27 00:00:00 | 4 Seasons Heating & Cooling   |           168    |     6953 | Purchase   | 4 Seasons Heating & Cooling   | 2025-06-27 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-27 00:00:00 | Better Proposals              |           137.98 |     6931 | Purchase   | Better Proposals              | 2025-06-27 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-27 00:00:00 | Lourdes Azpillaga             |          3503    |     6887 | Purchase   | Lourdes Azpillaga             | 2025-06-27 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-27 00:00:00 | Joanie Cassidy                |          2098.23 |     6886 | Purchase   | Joanie Cassidy                | 2025-06-27 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-27 00:00:00 | Mario Ricardo Velasquez Ramos |          2035.54 |     6885 | Purchase   | Mario Ricardo Velasquez Ramos | 2025-06-27 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-27 00:00:00 | Yarenis Fernandez             |          3782.54 |     6884 | Purchase   | Yarenis Fernandez             | 2025-06-27 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-26 00:00:00 | The Keg                       |           149.43 |     6952 | Purchase   | The Keg                       | 2025-06-26 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |
| 2025-06-26 00:00:00 | Bistro Inferno                |           120.96 |     6929 | Purchase   | Bistro Inferno                | 2025-06-26 00:00:00 |                    |             1 | amount_diff=0.00, date_diff=0, fuzzy_score=1.00 |

(Only first 15 of 714 rows shown above.)

