Python code:

```python
import pandas as pd
from metalake import load_data
import os

# Chat and file setup
chat_id = "68a34c07a64c103c6dc5b29f"
data_file_names = [
    'TD_bank_payments_Expenses.dat',
    'TD_bank_payments_Sheet1.dat',
    'TD_bank_payments_May2025.dat',
    'TD_bank_payments_no_vendor_May2025.dat',
    'TD_bank_payments_no_vendor_customer.dat',
    'TD_bank_payments_Profit and Loss Detail.dat',
    'TD_bank_payments_May2025total.dat',
    'TD_bank_payments_Profit and Loss Detail (1).dat',
    'TD_bank_payments_Sheet7.dat',
]
sheet_names = [
    'Expenses',
    'Sheet1',
    'May2025',
    'no_vendor_May2025',
    'no_vendor_customer',
    'Profit and Loss Detail',
    'May2025total',
    'Profit and Loss Detail (1)',
    'Sheet7'
]

# Keep track of row counts
sheet_row_counts = {}
df_list = []

for fname, sname in zip(data_file_names, sheet_names):
    df = load_data(chat_id, fname)
    if df.empty:
        print(f"Sheet '{sname}' (file: {fname}) is empty. Skipping.")
        sheet_row_counts[sname] = 0
        continue
    df['sheet_name'] = sname
    sheet_row_counts[sname] = len(df)
    df_list.append(df)

if not df_list:
    print("All sheets are empty. No data to combine.")
else:
    # Union merge: concat with ignore_index, aligning all columns
    combined_df = pd.concat(df_list, axis=0, ignore_index=True, sort=True)
    # Output file paths
    output_csv = 'files/TD_bank_payments_all_data.csv'
    output_dat = 'files/TD_bank_payments_all_data.dat'
    os.makedirs('files', exist_ok=True)
    combined_df.to_csv(output_csv, index=False)
    # Store as .dat (CSV format, per instructions)
    combined_df.to_csv(output_dat, index=False)
    # Console outputs
    print("\nRow counts per sheet:")
    for sname in sheet_names:
        print(f"  {sname} -> {sheet_row_counts.get(sname, 0)}")
    print(f"\nTotal rows in combined dataframe: {len(combined_df)}\n")
    # Show first 5 rows for structure confirmation
    preview_rows = combined_df.head(5)
    print("First 5 rows of combined dataframe:\n")
    print(preview_rows.to_markdown(index=False))
    if len(combined_df) > 5:
        print(f"\n(Only first 5 of {len(combined_df)} rows shown above.)")

````

Output:

Row counts per sheet:
  Expenses -> 95
  Sheet1 -> 13
  May2025 -> 40
  no_vendor_May2025 -> 22
  no_vendor_customer -> 16
  Profit and Loss Detail -> 188
  May2025total -> 81
  Profit and Loss Detail (1) -> 209
  Sheet7 -> 50

Total rows in combined dataframe: 714

First 5 rows of combined dataframe:

|     |   # |   AMOUNT | Action   |   Amount | Attachments   |   Balance | Category                |   Class | DATE   |   DESCRIPTION | Date       |   Memo/Description |   Name | No.   |   Ordinary Income/Expenses | Payee              |   Rule |   Sales tax |   Split |   Total |   Total before sales tax |   Transaction Type | Type    |   account_name |   account_sub_type |   amount_with_pst |   amount_without_pst |   customer_id |   pst_tax | selected   | sheet_name   |   tax_inclusive_flag |   total_adjusted_amount |   total_adjusted_amount_including_PST |   total_expense |   transaction_date |   transaction_id |   transaction_source |   transaction_type |   vendor_id |   vendor_key |   vendor_name |
|----:|----:|---------:|:---------|---------:|:--------------|----------:|:------------------------|--------:|:-------|--------------:|:-----------|-------------------:|-------:|:------|---------------------------:|:-------------------|-------:|------------:|--------:|--------:|-------------------------:|-------------------:|:--------|---------------:|-------------------:|------------------:|---------------------:|--------------:|----------:|:-----------|:-------------|---------------------:|------------------------:|--------------------------------------:|----------------:|-------------------:|-----------------:|---------------------:|-------------------:|------------:|-------------:|--------------:|
| nan | nan |      nan |          |      nan |               |       nan | Meals and entertainment |     nan | NaT    |           nan | 06/30/2025 |                nan |    nan |       |                        nan | Tuxedo Golf Course |    nan |        1.28 |     nan |   28.8  |                    27.52 |                nan | Expense |            nan |                nan |               nan |                  nan |           nan |       nan |            | Expenses     |                  nan |                     nan |                                   nan |             nan |                nan |              nan |                  nan |                nan |         nan |          nan |           nan |
| nan | nan |      nan |          |      nan |               |       nan | Meals and entertainment |     nan | NaT    |           nan | 06/30/2025 |                nan |    nan |       |                        nan | Tuxedo Golf Course |    nan |        1.21 |     nan |   27    |                    25.79 |                nan | Expense |            nan |                nan |               nan |                  nan |           nan |       nan |            | Expenses     |                  nan |                     nan |                                   nan |             nan |                nan |              nan |                  nan |                nan |         nan |          nan |           nan |
| nan | nan |      nan |          |      nan |               |       nan | Parking                 |     nan | NaT    |           nan | 06/30/2025 |                nan |    nan |       |                        nan | Uber               |    nan |        0    |     nan |    1    |                     1    |                nan | Expense |            nan |                nan |               nan |                  nan |           nan |       nan |            | Expenses     |                  nan |                     nan |                                   nan |             nan |                nan |              nan |                  nan |                nan |         nan |          nan |           nan |
| nan | nan |      nan |          |      nan |               |       nan | Marketing               |     nan | NaT    |           nan | 06/30/2025 |                nan |    nan |       |                        nan | Faber Creative     |    nan |       49.95 |     nan | 1048.95 |                   999    |                nan | Expense |            nan |                nan |               nan |                  nan |           nan |       nan |            | Expenses     |                  nan |                     nan |                                   nan |             nan |                nan |              nan |                  nan |                nan |         nan |          nan |           nan |
| nan | nan |      nan |          |      nan |               |       nan | Bank charges            |     nan | NaT    |           nan | 06/30/2025 |                nan |    nan |       |                        nan | TD Bank            |    nan |        0    |     nan |  125    |                   125    |                nan | Expense |            nan |                nan |               nan |                  nan |           nan |       nan |            | Expenses     |                  nan |                     nan |                                   nan |             nan |                nan |              nan |                  nan |                nan |         nan |          nan |           nan |

(Only first 5 of 714 rows shown above.)

