"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* LlmClient use for handle services related to LLM

* @class LlmClient
* @description LlmClient class is use to handle services related to LLM
* <AUTHOR>
"""

import traceback
from openai import AzureOpenAI, OpenAI
from dotenv import load_dotenv
import os
import json

from utils.constant import LLM_PROVIDER_AZURE_OPENAI, LLM_PROVIDER_OPENAI

load_dotenv()


class LlmClient:
    def __init__(self, provider, key, model, max_tokens):
        """
        Initialize LLM client
        """
        self.model = model
        self.provider = provider
        self.key = key
        self.max_tokens = max_tokens

        if provider == LLM_PROVIDER_OPENAI:
            self.client = OpenAI(api_key=key)
        elif provider == LLM_PROVIDER_AZURE_OPENAI:
            self.client = AzureOpenAI(
                api_key=key,
                api_version="2024-08-01-preview",
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            )
        else:
            raise ValueError("Invalid LLM provider")

    def get_llm_response(self, message_list, logger, response_format=None):

        logger.debug("\n------------------------------------------------")
        logger.debug(f"Invoke LLM | LLmClient.get_llm_response | >>>>  | Message list: {message_list}")

        if response_format is None:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=message_list,  # text inputs + image inputs
                max_tokens=self.max_tokens,
                temperature=0.7,
            )

        elif response_format == "json":
            response = self.client.chat.completions.create(
                model=self.model,
                response_format=response_format,
                messages=message_list,  # text inputs + image inputs
                max_tokens=self.max_tokens,
                temperature=0.7,
            )

        else:

            response = self.client.chat.completions.create(
                model=self.model,
                response_format={
                    "type": "json_schema",
                    "json_schema": response_format,
                },
                messages=message_list,  # text inputs + image inputs
                max_tokens=self.max_tokens,
                temperature=0.7,
            )

        if response:
            logger.debug(
                f"Invoke LLM | LLmClient.get_llm_response | <<<<  | Response: {response.choices[0].message.content}"
            )
            logger.debug("------------------------------------------------\n")
            return response.choices[0].message.content

        return None

    def validate_and_convert_json(self, json_string):
        if json_string is None:
            return None
        try:
            # Attempt to parse the JSON string
            json_dict = json.loads(json_string)
            return json_dict
        except json.JSONDecodeError as e:
            # If an error occurs, print the error message
            print(f"Invalid JSON: {e}")
            return None
