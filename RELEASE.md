# Documentation for releases of AI Agent Backend

## LN-V3.20.4

1. Determine the level of detail shown based on user type
2. Ability to query data from uploaded PDF documents 

## LN-V3.20.3

1. Integrated amazon SES with for email sending

## LN-V3.20.2

## LN-V3.20.2

1. Support .xls and .csv file processing
2. Enhancement to final answer presentation - prevent LLM from generating markdown tables
3. Save the current state when user stop the conversation.

## LN-V3.20.1

1. Include complex analysis related logs into log download zip file
2. Email send when share analysis with new template
3. Fix the issue with final answer formatting
4. Knowledge block sync with metalake
5. Email send for complex analysis questions that takes more time than defined threshold time in seconds.

## LN-V3.20.0

1. Agentic flow for solving complex queries.

## LN-V3.19.7

1. Add Api key update process when user add it from datalake frontend
2. Add daily Cronjob to send number of insight and analysis questions to layernext-cms
3. Improve the insight email template

## LN-V3.19.4

1. Insight Report can be share to other users. Shared users get email about the shared report.

## LN-V3.19.3

1. Insight Dashboard report can be pin to other users
2. Insight report sections can be edited,deleted ( Main Facts,Main Facts Visualizations, Actionable Recommendations, Other Facts )
3. Add Insight question show button in insight report
4. Business rules and data source overview sync

## LN-V3.19.2

1. follow up question for insight in dashboard
2. Insight board main graph rendering support according to chart.js

## LN-V3.19.1

1. Increase value distribution calculation limit to 25 distinct values
2. Return relationships and field stats to data dict sync

## LN-V3.19.0

1. insight dashboard feature

## LN-V3.18.2

1. Integrated agentic flow to do deep data analysis in case of continuous data retrieval failure
2. Parallel execution of insight hypothesis processing
3. Instruction tune-up for insight reasoning agent
4. Fixes to remove inefficiencies in SQL querying, restart process and data review

- Don't count visual failures as data retrieval failures
- Fixed the case of both csv and non-csv data in case of data review / response review
- Send the same data observation given to data review to the history which is input to the response analyser.
- Implemented the batch-wise querying execution at utility function 'run_sql_query'
- Response analyzer output - added parameters to verify the data retrieval is fine compared to previous attempt.

## LN-V3.18.0

1. Fixed bug on not properly updating conversation in status in conversation collections
2. Created new API for getting recent analysis
3. prompt history improvement for follow up analysis
4. Improvements in unstructured data processing - use of topic based high level categorization, re-using previous filtered labels in the conversation.
5. Fixed bug on not filtering recent analysis by user ID, and fixed bug on not updating the conversation status in conversation collections when insight is running

## LN-V3.17.1

1. data reviewer instruction tune-up
2. skip data reviewer for execution errors
3. Send used data section while both stream and history of a conversation
4. Fixed bug on extracting file paths when input string contains markdown content after file paths
5. Unstructured data processing - use pre-filled cache instead of on-the-fly processing.
6. fresh retry action agent run upon 3 time failures of data retrievals

## LN-V3.17.0

1. Change Report status when user Stop Insight
2. UX improvement - Show analysis steps while streaming

## LN-V3.16.5

Enhancements:

1. Code quality enhancement related to super admin userType
2. Fix issues with cache usage in unstructured data processing.
3. Ability to enable/disable user-wise question suggestion generation

## LN-V3.16.4

Enhancements:

1. Control the simultaneous insight runs by putting to a queue

Fixes:

1. Stop analysis questions properly when use

Bug Fixes:

1. Added measures to minimize the undesired outcomes by data reviewer

- Correctly set the data retrieval status to no records when nothing in csvs
- Add a prompt to invoke LLM call to re-write python code on data reviewer rejection

2. Prevention of quantitative data analysis on insufficient data

- Renamed is_further_analysis_required into is_quantitative_analysis_required to minimize wrong thought output

## LN-V3.16.3

Bug Fixes:

1. Fix data reviewer issue of not converging to the final answer by:
2. Breaking down the review process to two steps: textual and visual
3. Inputting the previous review history the textual review prompt
4. Disabling the data availability check

## LN-V3.16.2

1. Deeper analysis of data at before the insight generation at each thought step in REACT cycle.
2. Input the final status of each data retrieval in the REACT cycle to minimize the chance of reasoning agent include hypothetical data.
3. Fix UX issue occurred after adding Data reviewer feature
4. Added flexibility of switching models at different stages
5. Support the new reasoning models (o1-mini and o3-mini)
6. Suggested question generation and retrieval
7. Fix email template UI unresponsive issue
8. Fix the table truncation in insight react report
9. Send key findings of insight as an email
10. Use caching for speeding up the unstructured data processing

## LN-V3.16.1

1. Enable users to provide feedback on the hypothesis chain of thought.
2. Review the thought instruction (at REACT cycles) to make sure the action agent gets clear, focused and self-contained data retrieval instruction.
3. Maintain a summary of important data points in the REACT cycle to ensure the critical findings are not missed at intern report.
4. Fixed duplicate visualizations in appendix of master report
5. Instruction change to fix meaningless visualizations in appendix

## LN-V3.16.0

1. Support the hypothesis view with chain of thoughts in insight frontend
2. Enhancements for accuracy of insight generation - optimize the input for intern report generation, added missed hypothesis and current date/time
3. Code restructuring / cleanup in hypothesis processing and report generation to fix maintainability issues

## LN-V3.15.1

1. Enhancements for insight generation process to fix missing of critical observations at intern report generation, token limit hitting at master report generation, and provide linkage to observations in reports:

- Make the intern report structured JSON format.
- Added new step to identify the relevant insights from thought observations chain.
- Directly generate JSON format for master report - removed generation of markdown intermediate version.

2. Instruction update for unstructured DB data processing to fix the incorrect usage of the output of classification function.

## LN-V3.15.0

1. Fix the bug in simultaneous code execution with python exec
2. Enhancements to Insight agent to improve reliability and quality of report generated

- Fixed issues with csv and image file name list: Fixed the duplication of file names in subsequent data retrieval cycles, skip file names in case of failed retrievals, input two types of files separately to intern report generation to avoid confusions.
- To minimize the context of chain of thoughts / observations - Not adding to chain if the observation not useful (Converted the output of thought process to a JSON format and get a flag for useful or not)
- Get logic explanation together with action agent's answer and input it to the thought agent - to communicate how data was retrieved to thought agent
- Perform data availability check if detected as required by the Data review agent.

3. Integrated the DB unstructured data processing flow

## LN-V3.14.1

1. added AI generated text answer and csv answer to the response for internal API that used by metalake for evaluations
2. added Business Rules for chats

## LN-V3.14.0

Enhancements:

1. Use the same workflow for insight action and chat:

- Create child conversation for each action step
- Copy the messages under child conversations back to main insight conversation after completing each action

2. Instruction tune-up to handle large data record counts
3. Data reviewer: Provide additional output to determine whether there is a data issue - in case of insight - ignore the failure if its' not a data issue (i.e. just visualization issue)
4. Support @feedback facility - submit the user feedback comment to MetaLake via SDK function

5. Insight report new UI enhancement.

- Added feature to convert markdown formatted master insight report in to JSON object.
- Added new APIs for get insight report and share insight report.

Fixes:

Instruction addition for encouraging LLM to generate more meaningful file names to fix the confusions when selecting correct image while report generating

## LN-V3.13.1

Enhancements:

1. Add internal controller to communicate with metalake to get last llm action for data-dict evaluation

Bug fix:

1. Remove unwanted note section from the end of the insight final report

## LN-V3.12.1

Enhancements:

- Include the data type of each field during the data dictionary sync to LLM from MetaLake
- Give instructions to LLM to get table schema via get_table_schema metalake SDK function that filtered by visibility
- Add a Button to shared conversations to view chat in the App
- Use LLM based data review commonly for both chat and insights
- Tune-up of data reviewer agent for handling Null/NaN handling and too many record fetching error handling
- Review the intern and master insight reports and re-write if not presentable to user
- Insight title: generate one compatible with the UI design by LLM instructions instead of using insight question as it is.
- Removed confusions in SQL DB instructions with emphasis for doing calculations within SQL.
- Add Insight Icon instead of @ Insights | inside particular insight conversation title section, for Insights

Fixes:

- Insight react cycle - prevent same thoughts generating repeatedly by feeding the thoughts and observations in order as it is instead of separate arrays
- Fix the insight view and formatting
- Chat conversation Title disappear issue after refresh the page (Add new API to get the conversation Title by Id)

## LN-V3.12.0

Enhancements:

- KPI Evaluation and generate insight queries based on it
- Highlight generation from today insights and previous insight summary
- Send generated highlights to customer via email
- Maintain knowledge base for previous insights
- Review and feedback with LLM for observations at REACT cycle

Fixes:

- Shrink the ReACT cycle process in insights

## LN-V3.11.2

Fixes:

- Reduced the size of pre-data analysis input to single code writing by removing unnecessary information
- Ensured that file names are displayed instead of file keys (ObjectKey) in reference when document related question ask from chat.
- Replaced "Attachment" with the document's first question name when the first chat message is a document upload.

Fixes:

- Fix table truncate issue in insight generation

## LN-V3.11.1

Fixes:

- Code review instruction fix to handle pre-data analysis input

## LN-V3.11.0

Enhancements:

- Show error in frontend when openai error occured
- Document data extraction accuracy improvement with pre-data analysis

Fixes:

- Prevent hard coded pre-data analysis getting applied for other customers
- Show error in frontend when OpenAI error occurred
- Corrected content in document section "proposal" and add it to data dictionary mapping

## LN-V3.10

## LN-V3.10.1

## LN-V3.10.0

1. Rotate logs

- New environment variables added:

  1. LOG_BACKUP_COUNT - by default this is 90. This describes, size of the window which keeps logs files.

- Chat Title wise and Question wise search
- Select Favourite chats and filtered by them
- Filter chat history by date

## LN-V3.9.2 Sept 17th 2024

Enhancements:

- Flow the original question programmatically to document locate function
- Document locate batch size made configurable via env. variable

New environment variables added:

- DOCUMENT_PROCESS_PAGE_SIZE - Set the page size (batch size) for document processing. Default is 10

## LN-V3.8.0 August 15th 2024

Enhancements:

- Integrated new unstructured pdf processing functionality

New environment variables added:

- DATA_DICTIONARY_SYNC_DISABLED - To manually disable the data dictionary sync with MetaLake (If this is not set, the data dictionary sync is ON as usual)

## LN-V3.6.2 July 10th 2024

Enhancements:

- Use of thread pool for conversation execution instead of threads for each chat
- Save the conversation messages at intermediate steps in insight generation
- Pre-defined goal tracking for PDRMax after removing the add goal button
- Facility to execute python code and get quantitative data when needed by intern insight report generator
- chat history - included goal tracking insight conversations as well

Bug Fixes:

- Super lube: corrected wording 'Tyre' to 'Tire' (american english)

New environment variables added:

- Daily objective tracking will run only if these are added to .env
  DAILY_TRIGGER_HOUR=09
  DAILY_TRIGGER_MINUTE=17

## LN-V3.6.1 June 25th 2024

New features:

- Goal tracking graph - integrated forecast and goal projection of revenue / job completions in PDRMax
- Chat app: Added stop button
  Enhancements:
- Goal tracking graph: Add Labels for goal and current value
- Goal tracking graph accuracy improvements by instruction tuneups
- Progressive loading of data to frontend (Goal tracking data first)
- Ability to Edit and delete of objective
- Frontend UI improvements of goal tracking graph

## LN-V3.6.0 June 11th 2024

New features:

- Objectives section: Goal Tracking with insights
- Unstructured data processing: Enabled batch-wise processing of comments/notes
- Added stop button to chat
  Enhancements:
- UI improvements in chat
  Bug fixes:
- PDRMax: corrected the planned completion date of jobs - Fix the delayed job count
